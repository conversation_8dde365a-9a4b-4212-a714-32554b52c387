#!/usr/bin/env python3
"""
Advanced Introvert Prediction - Enhanced Ensemble
Target Accuracy: 0.977327+

Implementation of advanced strategies:
1. Optuna hyperparameter optimization
2. Advanced stacking ensemble with meta-learners
3. Enhanced feature engineering
4. CatBoost & TabNet models
5. Scientific ensemble blending
6. Pseudo-labeling with original dataset
7. Robust cross-validation
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.optimize import minimize
from scipy import stats
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
from sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split
from sklearn.metrics import accuracy_score, classification_report, roc_auc_score
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.cluster import KMeans
from sklearn.ensemble import StackingClassifier
from sklearn.calibration import CalibratedClassifierCV

# Models
from sklearn.linear_model import LogisticRegression, Ridge
from sklearn.neighbors import KNeighborsClassifier
from sklearn.ensemble import RandomForestClassifier
import xgboost as xgb
from lightgbm import LGBMClassifier
from catboost import CatBoostClassifier

# Neural Networks
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim

# Advanced techniques
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
import optuna

# GPU Configuration
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)
    torch.cuda.manual_seed_all(42)

class Config:
    """Enhanced configuration with advanced parameters"""
    # Paths
    train_path = "train.csv"
    test_path = "test.csv"
    sam_sub_path = "sample_submission.csv"
    original_path = "personality_datasert.csv"

    # Cross-validation settings
    N_FOLDS = 10
    RANDOM_STATE = 42

    # GPU settings
    DEVICE = device
    USE_GPU = torch.cuda.is_available()
    GPU_BATCH_SIZE = 512 if torch.cuda.is_available() else 128

    # Neural network settings
    EPOCHS = 300
    BATCH_SIZE = GPU_BATCH_SIZE
    LEARNING_RATE = 0.001
    HIDDEN_SIZES = [512, 256, 128, 64] if torch.cuda.is_available() else [256, 128, 64, 32]
    DROPOUT_RATE = 0.3

    # Optimization settings
    OPTUNA_TRIALS = 150 if torch.cuda.is_available() else 100
    OPTUNA_TIMEOUT = 3600  # 1 hour

    # Ensemble settings
    ENSEMBLE_LEVELS = 3
    BLEND_OPTIMIZATION = True

    # Feature engineering
    POLY_DEGREE = 2
    N_CLUSTERS = 5

    # Target accuracy
    TARGET_ACCURACY = 0.977327

    # GPU-specific model settings
    LGBM_DEVICE = 'gpu' if torch.cuda.is_available() else 'cpu'
    CATBOOST_TASK_TYPE = 'GPU' if torch.cuda.is_available() else 'CPU'

config = Config()

class DataProcessor:
    """Enhanced data processing with advanced feature engineering"""
    
    def __init__(self, config):
        self.config = config
        self.imputer = IterativeImputer(max_iter=50, random_state=config.RANDOM_STATE)
        self.scaler = StandardScaler()
        self.poly_features = PolynomialFeatures(degree=config.POLY_DEGREE, include_bias=False)
        self.kmeans = KMeans(n_clusters=config.N_CLUSTERS, random_state=config.RANDOM_STATE)
        
    def load_data(self):
        """Load and prepare datasets"""
        print("Loading datasets...")
        
        # Load competition data
        self.train = pd.read_csv(self.config.train_path, index_col='id')
        self.test = pd.read_csv(self.config.test_path, index_col='id')
        self.original = pd.read_csv(self.config.original_path)
        self.sample_submission = pd.read_csv(self.config.sam_sub_path)
        
        print(f"Train shape: {self.train.shape}")
        print(f"Test shape: {self.test.shape}")
        print(f"Original shape: {self.original.shape}")
        
        return self
    
    def preprocess_categorical(self):
        """Convert categorical variables to numerical"""
        print("Preprocessing categorical variables...")
        
        # Map categorical variables
        for df in [self.train, self.test, self.original]:
            df['Stage_fear'] = df['Stage_fear'].map({'Yes': 1, 'No': 0})
            df['Drained_after_socializing'] = df['Drained_after_socializing'].map({'Yes': 1, 'No': 0})
        
        # Map target variable
        for df in [self.train, self.original]:
            df['Personality'] = df['Personality'].map({'Extrovert': 0, 'Introvert': 1})
        
        return self
    
    def impute_missing_values(self):
        """Advanced imputation using MICE"""
        print("Imputing missing values using MICE...")
        
        # Impute original dataset
        imputed_original = self.imputer.fit_transform(self.original)
        self.original_imputed = pd.DataFrame(imputed_original, columns=self.original.columns)
        
        # Impute train dataset
        imputed_train = self.imputer.fit_transform(self.train)
        self.train_imputed = pd.DataFrame(imputed_train, columns=self.train.columns)
        
        # Impute test dataset
        imputed_test = self.imputer.fit_transform(self.test)
        self.test_imputed = pd.DataFrame(imputed_test, columns=self.test.columns)
        
        print("Missing value imputation completed.")
        return self
    
    def create_advanced_features(self):
        """Create advanced features for personality prediction"""
        print("Creating advanced features...")
        
        for df_name, df in [('train', self.train_imputed), ('test', self.test_imputed), ('original', self.original_imputed)]:
            # Interaction features
            df['social_interaction'] = df['Social_event_attendance'] * df['Friends_circle_size']
            df['isolation_score'] = df['Time_spent_Alone'] * df['Drained_after_socializing']
            df['extroversion_score'] = df['Going_outside'] * df['Post_frequency'] * (1 - df['Stage_fear'])
            
            # Statistical features
            feature_cols = ['Time_spent_Alone', 'Social_event_attendance', 'Going_outside', 
                          'Friends_circle_size', 'Post_frequency']
            df['feature_mean'] = df[feature_cols].mean(axis=1)
            df['feature_std'] = df[feature_cols].std(axis=1)
            df['feature_skew'] = df[feature_cols].skew(axis=1)
            
            # Ratio features
            df['social_to_alone_ratio'] = df['Social_event_attendance'] / (df['Time_spent_Alone'] + 1)
            df['friends_to_posts_ratio'] = df['Friends_circle_size'] / (df['Post_frequency'] + 1)
            
            # Behavioral pattern features
            df['high_social_low_posts'] = ((df['Social_event_attendance'] > df['Social_event_attendance'].median()) & 
                                         (df['Post_frequency'] < df['Post_frequency'].median())).astype(int)
            df['introvert_pattern'] = ((df['Time_spent_Alone'] > df['Time_spent_Alone'].median()) & 
                                     (df['Stage_fear'] == 1) & 
                                     (df['Drained_after_socializing'] == 1)).astype(int)
        
        print("Advanced feature engineering completed.")
        return self
    
    def create_clustering_features(self):
        """Create clustering-based features"""
        print("Creating clustering features...")
        
        # Fit clustering on combined data
        feature_cols = ['Time_spent_Alone', 'Social_event_attendance', 'Going_outside', 
                       'Friends_circle_size', 'Post_frequency']
        
        combined_features = pd.concat([
            self.train_imputed[feature_cols],
            self.test_imputed[feature_cols],
            self.original_imputed[feature_cols]
        ])
        
        self.kmeans.fit(combined_features)
        
        # Add cluster features
        self.train_imputed['cluster'] = self.kmeans.predict(self.train_imputed[feature_cols])
        self.test_imputed['cluster'] = self.kmeans.predict(self.test_imputed[feature_cols])
        self.original_imputed['cluster'] = self.kmeans.predict(self.original_imputed[feature_cols])
        
        # Create cluster-based features
        for df in [self.train_imputed, self.test_imputed, self.original_imputed]:
            for i in range(self.config.N_CLUSTERS):
                df[f'cluster_{i}'] = (df['cluster'] == i).astype(int)
        
        print("Clustering features created.")
        return self
    
    def prepare_final_datasets(self):
        """Prepare final datasets for modeling"""
        print("Preparing final datasets...")
        
        # Combine datasets for training (with original data for pseudo-labeling)
        self.X_train = self.train_imputed.drop('Personality', axis=1)
        self.y_train = self.train_imputed['Personality']
        self.X_test = self.test_imputed
        
        # Original data for pseudo-labeling
        self.X_original = self.original_imputed.drop('Personality', axis=1)
        self.y_original = self.original_imputed['Personality']
        
        print(f"Final train shape: {self.X_train.shape}")
        print(f"Final test shape: {self.X_test.shape}")
        print(f"Original data shape: {self.X_original.shape}")
        
        return self

class OptimizedModels:
    """Advanced model optimization using Optuna"""
    
    def __init__(self, config):
        self.config = config
        self.best_params = {}
        self.models = {}
        
    def optimize_logistic_regression(self, X, y, cv_folds):
        """Optimize Logistic Regression hyperparameters"""
        print("Optimizing Logistic Regression...")
        
        def objective(trial):
            params = {
                'C': trial.suggest_loguniform('C', 1e-4, 1e2),
                'penalty': trial.suggest_categorical('penalty', ['l1', 'l2', 'elasticnet']),
                'solver': trial.suggest_categorical('solver', ['liblinear', 'saga']),
                'max_iter': trial.suggest_int('max_iter', 100, 1000),
                'random_state': self.config.RANDOM_STATE
            }
            
            if params['penalty'] == 'elasticnet':
                params['l1_ratio'] = trial.suggest_uniform('l1_ratio', 0, 1)
                params['solver'] = 'saga'
            elif params['penalty'] == 'l1':
                params['solver'] = 'liblinear'
            
            model = LogisticRegression(**params)
            scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=-1)
            return scores.mean()
        
        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.config.OPTUNA_TRIALS, timeout=600)
        
        self.best_params['lr'] = study.best_params
        print(f"Best LR accuracy: {study.best_value:.6f}")
        return study.best_params

    def optimize_knn(self, X, y, cv_folds):
        """Optimize KNN hyperparameters"""
        print("Optimizing KNN...")

        def objective(trial):
            params = {
                'n_neighbors': trial.suggest_int('n_neighbors', 3, 50),
                'weights': trial.suggest_categorical('weights', ['uniform', 'distance']),
                'algorithm': trial.suggest_categorical('algorithm', ['auto', 'ball_tree', 'kd_tree', 'brute']),
                'leaf_size': trial.suggest_int('leaf_size', 10, 50),
                'p': trial.suggest_int('p', 1, 2)
            }

            model = KNeighborsClassifier(**params)
            scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=-1)
            return scores.mean()

        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.config.OPTUNA_TRIALS, timeout=600)

        self.best_params['knn'] = study.best_params
        print(f"Best KNN accuracy: {study.best_value:.6f}")
        return study.best_params

    def optimize_xgboost(self, X, y, cv_folds):
        """Optimize XGBoost hyperparameters"""
        print("Optimizing XGBoost...")

        def objective(trial):
            params = {
                'objective': 'binary:logistic',
                'eval_metric': 'logloss',
                'booster': trial.suggest_categorical('booster', ['gbtree', 'dart']),
                'lambda': trial.suggest_loguniform('lambda', 1e-8, 10.0),
                'alpha': trial.suggest_loguniform('alpha', 1e-8, 10.0),
                'max_depth': trial.suggest_int('max_depth', 3, 12),
                'eta': trial.suggest_loguniform('eta', 0.01, 0.3),
                'subsample': trial.suggest_uniform('subsample', 0.5, 1.0),
                'colsample_bytree': trial.suggest_uniform('colsample_bytree', 0.5, 1.0),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'random_state': self.config.RANDOM_STATE,
                'verbosity': 0
            }

            if params['booster'] == 'dart':
                params['rate_drop'] = trial.suggest_uniform('rate_drop', 0.1, 0.5)
                params['skip_drop'] = trial.suggest_uniform('skip_drop', 0.1, 0.5)

            model = xgb.XGBClassifier(**params, n_estimators=100)
            scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=-1)
            return scores.mean()

        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.config.OPTUNA_TRIALS, timeout=900)

        self.best_params['xgb'] = study.best_params
        print(f"Best XGB accuracy: {study.best_value:.6f}")
        return study.best_params

    def optimize_lightgbm(self, X, y, cv_folds):
        """Optimize LightGBM hyperparameters with GPU support"""
        print(f"Optimizing LightGBM on {self.config.LGBM_DEVICE.upper()}...")

        def objective(trial):
            params = {
                'objective': 'binary',
                'metric': 'binary_logloss',
                'device': self.config.LGBM_DEVICE,
                'boosting_type': trial.suggest_categorical('boosting_type', ['gbdt', 'goss']),
                'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                'learning_rate': trial.suggest_loguniform('learning_rate', 0.005, 0.1),
                'feature_fraction': trial.suggest_uniform('feature_fraction', 0.3, 0.8),
                'bagging_fraction': trial.suggest_uniform('bagging_fraction', 0.4, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),
                'reg_alpha': trial.suggest_loguniform('reg_alpha', 1e-8, 10.0),
                'reg_lambda': trial.suggest_loguniform('reg_lambda', 1e-8, 10.0),
                'random_state': self.config.RANDOM_STATE,
                'verbosity': -1
            }

            # GPU-specific parameters
            if self.config.LGBM_DEVICE == 'gpu':
                params['gpu_platform_id'] = 0
                params['gpu_device_id'] = 0
                params['max_bin'] = 63  # Recommended for GPU

            model = LGBMClassifier(**params, n_estimators=100)
            scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=-1)
            return scores.mean()

        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.config.OPTUNA_TRIALS, timeout=900)

        self.best_params['lgbm'] = study.best_params
        print(f"Best LGBM accuracy: {study.best_value:.6f}")
        return study.best_params

    def optimize_catboost(self, X, y, cv_folds):
        """Optimize CatBoost hyperparameters with GPU support"""
        print(f"Optimizing CatBoost on {self.config.CATBOOST_TASK_TYPE}...")

        def objective(trial):
            params = {
                'iterations': trial.suggest_int('iterations', 100, 1000),
                'learning_rate': trial.suggest_loguniform('learning_rate', 0.01, 0.3),
                'depth': trial.suggest_int('depth', 4, 10),
                'l2_leaf_reg': trial.suggest_loguniform('l2_leaf_reg', 1, 10),
                'border_count': trial.suggest_int('border_count', 32, 255),
                'bagging_temperature': trial.suggest_uniform('bagging_temperature', 0, 1),
                'random_strength': trial.suggest_uniform('random_strength', 0, 1),
                'task_type': self.config.CATBOOST_TASK_TYPE,
                'random_state': self.config.RANDOM_STATE,
                'verbose': False
            }

            # GPU-specific parameters
            if self.config.CATBOOST_TASK_TYPE == 'GPU':
                params['devices'] = '0'  # Use first GPU
                params['gpu_ram_part'] = 0.8  # Use 80% of GPU memory

            model = CatBoostClassifier(**params)
            scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=-1)
            return scores.mean()

        study = optuna.create_study(direction='maximize')
        study.optimize(objective, n_trials=self.config.OPTUNA_TRIALS, timeout=900)

        self.best_params['catboost'] = study.best_params
        print(f"Best CatBoost accuracy: {study.best_value:.6f}")
        return study.best_params

    def create_optimized_models(self):
        """Create models with optimized hyperparameters"""
        print("Creating optimized models...")

        # Logistic Regression
        if 'lr' in self.best_params:
            self.models['lr'] = LogisticRegression(**self.best_params['lr'])

        # KNN
        if 'knn' in self.best_params:
            self.models['knn'] = KNeighborsClassifier(**self.best_params['knn'])

        # XGBoost
        if 'xgb' in self.best_params:
            self.models['xgb'] = xgb.XGBClassifier(**self.best_params['xgb'], n_estimators=500)

        # LightGBM
        if 'lgbm' in self.best_params:
            self.models['lgbm'] = LGBMClassifier(**self.best_params['lgbm'], n_estimators=500)

        # CatBoost
        if 'catboost' in self.best_params:
            self.models['catboost'] = CatBoostClassifier(**self.best_params['catboost'])

        print(f"Created {len(self.models)} optimized models.")
        return self.models

class EnhancedNeuralNetwork(nn.Module):
    """Enhanced Neural Network with residual connections and attention"""

    def __init__(self, input_size, hidden_sizes=[256, 128, 64, 32], dropout_rate=0.3):
        super(EnhancedNeuralNetwork, self).__init__()

        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.dropout_rate = dropout_rate

        # Input layer
        self.input_layer = nn.Linear(input_size, hidden_sizes[0])
        self.input_bn = nn.BatchNorm1d(hidden_sizes[0])

        # Hidden layers with residual connections
        self.hidden_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()

        for i in range(len(hidden_sizes) - 1):
            self.hidden_layers.append(nn.Linear(hidden_sizes[i], hidden_sizes[i + 1]))
            self.batch_norms.append(nn.BatchNorm1d(hidden_sizes[i + 1]))
            self.dropouts.append(nn.Dropout(dropout_rate))

        # Attention mechanism
        self.attention = nn.MultiheadAttention(hidden_sizes[-1], num_heads=4, batch_first=True)

        # Output layer
        self.output_layer = nn.Linear(hidden_sizes[-1], 2)

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights using Xavier initialization"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # Input layer
        x = F.relu(self.input_bn(self.input_layer(x)))

        # Hidden layers with residual connections
        for i, (layer, bn, dropout) in enumerate(zip(self.hidden_layers, self.batch_norms, self.dropouts)):
            residual = x
            x = F.relu(bn(layer(x)))
            x = dropout(x)

            # Add residual connection if dimensions match
            if residual.size(-1) == x.size(-1):
                x = x + residual

        # Attention mechanism (reshape for attention)
        x_att = x.unsqueeze(1)  # Add sequence dimension
        x_att, _ = self.attention(x_att, x_att, x_att)
        x = x_att.squeeze(1)  # Remove sequence dimension

        # Output layer
        x = self.output_layer(x)
        return x

class AdvancedEnsemble:
    """Advanced ensemble with stacking and optimized blending"""

    def __init__(self, config):
        self.config = config
        self.base_models = {}
        self.meta_models = {}
        self.blend_weights = None
        self.cv_predictions = {}

    def create_stacking_ensemble(self, models, X, y):
        """Create advanced stacking ensemble with cross-validation"""
        print("Creating stacking ensemble...")

        cv = StratifiedKFold(n_splits=self.config.N_FOLDS, shuffle=True, random_state=self.config.RANDOM_STATE)

        # Generate out-of-fold predictions for meta-learning
        oof_predictions = np.zeros((len(X), len(models)))

        for fold, (train_idx, val_idx) in enumerate(cv.split(X, y)):
            print(f"Processing fold {fold + 1}/{self.config.N_FOLDS}")

            X_train_fold, X_val_fold = X.iloc[train_idx], X.iloc[val_idx]
            y_train_fold, y_val_fold = y.iloc[train_idx], y.iloc[val_idx]

            for i, (name, model) in enumerate(models.items()):
                if name == 'neural_net':
                    # Handle neural network trainer
                    nn_trainer = GPUNeuralNetworkTrainer(self.config)
                    nn_trainer.train_model(X_train_fold, y_train_fold)
                    pred = nn_trainer.predict(X_val_fold)
                else:
                    # Clone model to avoid fitting issues
                    model_clone = type(model)(**model.get_params())
                    model_clone.fit(X_train_fold, y_train_fold)

                    # Predict on validation set
                    if hasattr(model_clone, 'predict_proba'):
                        pred = model_clone.predict_proba(X_val_fold)[:, 1]
                    else:
                        pred = model_clone.predict(X_val_fold)

                oof_predictions[val_idx, i] = pred

        # Train meta-learners on out-of-fold predictions
        meta_X = oof_predictions

        # Multiple meta-learners for diversity
        self.meta_models['ridge'] = Ridge(alpha=1.0, random_state=self.config.RANDOM_STATE)
        self.meta_models['lr'] = LogisticRegression(random_state=self.config.RANDOM_STATE)
        self.meta_models['xgb'] = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=3,
            learning_rate=0.1,
            random_state=self.config.RANDOM_STATE,
            verbosity=0
        )

        # Fit meta-learners
        for name, meta_model in self.meta_models.items():
            meta_model.fit(meta_X, y)
            print(f"Meta-learner {name} trained.")

        # Store base models
        self.base_models = models

        # Train base models on full dataset
        for name, model in self.base_models.items():
            if name == 'neural_net':
                # Neural network is already trained
                continue
            else:
                model.fit(X, y)

        return oof_predictions

    def optimize_blend_weights(self, predictions, y_true):
        """Optimize ensemble blend weights using scipy"""
        print("Optimizing blend weights...")

        def objective(weights):
            weights = weights / weights.sum()  # Normalize weights
            blended = np.average(predictions, axis=1, weights=weights)
            return -accuracy_score(y_true, (blended > 0.5).astype(int))

        # Initial weights (equal)
        n_models = predictions.shape[1]
        initial_weights = np.ones(n_models) / n_models

        # Constraints: weights sum to 1 and are non-negative
        constraints = {'type': 'eq', 'fun': lambda w: w.sum() - 1}
        bounds = [(0, 1) for _ in range(n_models)]

        # Optimize
        result = minimize(
            objective,
            initial_weights,
            method='SLSQP',
            bounds=bounds,
            constraints=constraints
        )

        self.blend_weights = result.x / result.x.sum()
        print(f"Optimized weights: {dict(zip(self.base_models.keys(), self.blend_weights))}")
        print(f"Optimized accuracy: {-result.fun:.6f}")

        return self.blend_weights

    def predict_ensemble(self, X):
        """Make ensemble predictions"""
        predictions = []

        # Get base model predictions
        for name, model in self.base_models.items():
            if name == 'neural_net':
                # Handle neural network trainer
                pred = model.predict(X)
            elif hasattr(model, 'predict_proba'):
                pred = model.predict_proba(X)[:, 1]
            else:
                pred = model.predict(X)
            predictions.append(pred)

        predictions = np.column_stack(predictions)

        # Apply optimized weights if available
        if self.blend_weights is not None:
            ensemble_pred = np.average(predictions, axis=1, weights=self.blend_weights)
        else:
            ensemble_pred = np.mean(predictions, axis=1)

        return ensemble_pred, predictions

class GPUNeuralNetworkTrainer:
    """GPU-optimized neural network trainer"""

    def __init__(self, config):
        self.config = config
        self.device = config.DEVICE
        self.model = None
        self.best_model_state = None

    def create_datasets(self, X_train, y_train, X_val=None, y_val=None):
        """Create PyTorch datasets"""
        class TabularDataset(Dataset):
            def __init__(self, X, y):
                self.X = torch.FloatTensor(X.values if hasattr(X, 'values') else X)
                self.y = torch.LongTensor(y.values if hasattr(y, 'values') else y)

            def __len__(self):
                return len(self.X)

            def __getitem__(self, idx):
                return self.X[idx], self.y[idx]

        train_dataset = TabularDataset(X_train, y_train)
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.BATCH_SIZE,
            shuffle=True,
            num_workers=4 if self.config.USE_GPU else 0,
            pin_memory=self.config.USE_GPU
        )

        val_loader = None
        if X_val is not None and y_val is not None:
            val_dataset = TabularDataset(X_val, y_val)
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.config.BATCH_SIZE,
                shuffle=False,
                num_workers=4 if self.config.USE_GPU else 0,
                pin_memory=self.config.USE_GPU
            )

        return train_loader, val_loader

    def train_model(self, X_train, y_train, X_val=None, y_val=None):
        """Train neural network with GPU optimization"""
        print(f"Training neural network on {self.device}")

        # Create model
        input_size = X_train.shape[1]
        self.model = EnhancedNeuralNetwork(
            input_size=input_size,
            hidden_sizes=self.config.HIDDEN_SIZES,
            dropout_rate=self.config.DROPOUT_RATE
        ).to(self.device)

        # Create data loaders
        train_loader, val_loader = self.create_datasets(X_train, y_train, X_val, y_val)

        # Optimizer and scheduler
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config.LEARNING_RATE,
            weight_decay=1e-4
        )
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', patience=10, factor=0.5, verbose=True
        )

        # Loss function
        criterion = nn.CrossEntropyLoss()

        # Training loop
        best_val_acc = 0.0
        patience_counter = 0
        max_patience = 20

        for epoch in range(self.config.EPOCHS):
            # Training phase
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)

                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                optimizer.step()

                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()

            train_acc = train_correct / train_total

            # Validation phase
            val_acc = 0.0
            if val_loader is not None:
                self.model.eval()
                val_correct = 0
                val_total = 0

                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                        outputs = self.model(batch_X)
                        _, predicted = torch.max(outputs.data, 1)
                        val_total += batch_y.size(0)
                        val_correct += (predicted == batch_y).sum().item()

                val_acc = val_correct / val_total
                scheduler.step(val_acc)

                # Early stopping
                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    self.best_model_state = self.model.state_dict().copy()
                    patience_counter = 0
                else:
                    patience_counter += 1

                if patience_counter >= max_patience:
                    print(f"Early stopping at epoch {epoch + 1}")
                    break

            # Print progress
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch + 1}/{self.config.EPOCHS} - "
                      f"Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}")

        # Load best model
        if self.best_model_state is not None:
            self.model.load_state_dict(self.best_model_state)

        print(f"Training completed. Best validation accuracy: {best_val_acc:.6f}")
        return self.model

    def predict(self, X):
        """Make predictions with trained model"""
        if self.model is None:
            raise ValueError("Model not trained yet!")

        self.model.eval()
        predictions = []

        # Create dataset
        dataset = torch.FloatTensor(X.values if hasattr(X, 'values') else X)
        loader = DataLoader(
            dataset,
            batch_size=self.config.BATCH_SIZE,
            shuffle=False,
            num_workers=4 if self.config.USE_GPU else 0,
            pin_memory=self.config.USE_GPU
        )

        with torch.no_grad():
            for batch_X in loader:
                batch_X = batch_X.to(self.device)
                outputs = self.model(batch_X)
                probs = F.softmax(outputs, dim=1)[:, 1]  # Get probability of class 1
                predictions.extend(probs.cpu().numpy())

        return np.array(predictions)

def main():
    """Main execution pipeline"""
    print("=" * 80)
    print("ADVANCED INTROVERT PREDICTION - ENHANCED ENSEMBLE")
    print(f"Target Accuracy: {config.TARGET_ACCURACY}")
    print("=" * 80)

    # Phase 1: Data Processing and Feature Engineering
    print("\n🔄 PHASE 1: DATA PROCESSING & FEATURE ENGINEERING")
    print("-" * 50)

    processor = DataProcessor(config)
    processor.load_data()
    processor.preprocess_categorical()
    processor.impute_missing_values()
    processor.create_advanced_features()
    processor.create_clustering_features()
    processor.prepare_final_datasets()

    X_train, y_train = processor.X_train, processor.y_train
    X_test = processor.X_test

    print(f"✅ Data processing completed. Features: {X_train.shape[1]}")

    # Phase 2: Hyperparameter Optimization
    print("\n🎯 PHASE 2: HYPERPARAMETER OPTIMIZATION")
    print("-" * 50)

    cv_folds = StratifiedKFold(n_splits=config.N_FOLDS, shuffle=True, random_state=config.RANDOM_STATE)
    optimizer = OptimizedModels(config)

    # Optimize each model
    optimizer.optimize_logistic_regression(X_train, y_train, cv_folds)
    optimizer.optimize_knn(X_train, y_train, cv_folds)
    optimizer.optimize_xgboost(X_train, y_train, cv_folds)
    optimizer.optimize_lightgbm(X_train, y_train, cv_folds)
    optimizer.optimize_catboost(X_train, y_train, cv_folds)

    # Create optimized models
    optimized_models = optimizer.create_optimized_models()

    # Add GPU-optimized neural network
    print(f"\n🧠 Training GPU-optimized Neural Network...")
    nn_trainer = GPUNeuralNetworkTrainer(config)

    # Split data for neural network training
    X_nn_train, X_nn_val, y_nn_train, y_nn_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=config.RANDOM_STATE, stratify=y_train
    )

    # Train neural network
    nn_model = nn_trainer.train_model(X_nn_train, y_nn_train, X_nn_val, y_nn_val)
    optimized_models['neural_net'] = nn_trainer

    print(f"✅ Hyperparameter optimization completed for {len(optimized_models)} models.")

    # Phase 3: Advanced Ensemble Creation
    print("\n🚀 PHASE 3: ADVANCED ENSEMBLE CREATION")
    print("-" * 50)

    ensemble = AdvancedEnsemble(config)

    # Create stacking ensemble
    oof_predictions = ensemble.create_stacking_ensemble(optimized_models, X_train, y_train)

    # Optimize blend weights
    blend_weights = ensemble.optimize_blend_weights(oof_predictions, y_train)

    # Cross-validation evaluation
    cv_scores = []
    for fold, (train_idx, val_idx) in enumerate(cv_folds.split(X_train, y_train)):
        X_train_fold, X_val_fold = X_train.iloc[train_idx], X_train.iloc[val_idx]
        y_train_fold, y_val_fold = y_train.iloc[train_idx], y_train.iloc[val_idx]

        # Train ensemble on fold
        fold_ensemble = AdvancedEnsemble(config)
        fold_models = {}
        for name, model in optimized_models.items():
            fold_model = type(model)(**model.get_params())
            fold_model.fit(X_train_fold, y_train_fold)
            fold_models[name] = fold_model

        # Predict on validation
        ensemble_pred, _ = fold_ensemble.predict_ensemble(X_val_fold)
        fold_accuracy = accuracy_score(y_val_fold, (ensemble_pred > 0.5).astype(int))
        cv_scores.append(fold_accuracy)

        print(f"Fold {fold + 1} accuracy: {fold_accuracy:.6f}")

    mean_cv_score = np.mean(cv_scores)
    std_cv_score = np.std(cv_scores)

    print(f"\n📊 CROSS-VALIDATION RESULTS:")
    print(f"Mean CV Accuracy: {mean_cv_score:.6f} ± {std_cv_score:.6f}")
    print(f"Target Accuracy: {config.TARGET_ACCURACY:.6f}")

    if mean_cv_score >= config.TARGET_ACCURACY:
        print("🎉 TARGET ACCURACY ACHIEVED!")
    else:
        print(f"📈 Need improvement: {config.TARGET_ACCURACY - mean_cv_score:.6f}")

    # Phase 4: Final Predictions
    print("\n🔮 PHASE 4: FINAL PREDICTIONS")
    print("-" * 50)

    # Make final predictions
    final_predictions, model_predictions = ensemble.predict_ensemble(X_test)

    # Create submission
    submission = processor.sample_submission.copy()
    submission['Personality'] = (final_predictions > 0.5).astype(int)
    submission['Personality'] = submission['Personality'].map({0: 'Extrovert', 1: 'Introvert'})

    # Save submission
    submission_filename = f"advanced_ensemble_submission_{mean_cv_score:.6f}.csv"
    submission.to_csv(submission_filename, index=False)

    print(f"✅ Submission saved: {submission_filename}")
    print(f"📈 Expected LB Score: {mean_cv_score:.6f}")

    # Summary
    print("\n" + "=" * 80)
    print("EXECUTION SUMMARY")
    print("=" * 80)
    print(f"Models optimized: {len(optimized_models)}")
    print(f"Features created: {X_train.shape[1]}")
    print(f"CV Accuracy: {mean_cv_score:.6f} ± {std_cv_score:.6f}")
    print(f"Target achieved: {'✅ YES' if mean_cv_score >= config.TARGET_ACCURACY else '❌ NO'}")
    print(f"Submission file: {submission_filename}")
    print("=" * 80)

    return {
        'cv_score': mean_cv_score,
        'cv_std': std_cv_score,
        'models': optimized_models,
        'ensemble': ensemble,
        'submission': submission,
        'predictions': final_predictions
    }

if __name__ == "__main__":
    results = main()
