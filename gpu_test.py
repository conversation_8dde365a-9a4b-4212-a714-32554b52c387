#!/usr/bin/env python3
"""
GPU Test Script for Advanced Introvert Prediction
Tests GPU availability and performance for different components
"""

import torch
import numpy as np
import pandas as pd
import time
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split

# Test GPU availability
print("=" * 60)
print("GPU AVAILABILITY TEST")
print("=" * 60)

# Check PyTorch GPU support
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA version: {torch.version.cuda}")
    print(f"Number of GPUs: {torch.cuda.device_count()}")
    
    for i in range(torch.cuda.device_count()):
        gpu_props = torch.cuda.get_device_properties(i)
        print(f"GPU {i}: {gpu_props.name}")
        print(f"  Memory: {gpu_props.total_memory / 1024**3:.1f} GB")
        print(f"  Compute Capability: {gpu_props.major}.{gpu_props.minor}")
    
    device = torch.device('cuda')
    print(f"Using device: {device}")
else:
    device = torch.device('cpu')
    print(f"Using device: {device} (GPU not available)")

print("\n" + "=" * 60)
print("NEURAL NETWORK GPU PERFORMANCE TEST")
print("=" * 60)

# Create synthetic data similar to personality prediction
X, y = make_classification(
    n_samples=10000,
    n_features=20,
    n_informative=15,
    n_redundant=5,
    n_classes=2,
    random_state=42
)

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Convert to tensors
X_train_tensor = torch.FloatTensor(X_train).to(device)
y_train_tensor = torch.LongTensor(y_train).to(device)
X_test_tensor = torch.FloatTensor(X_test).to(device)
y_test_tensor = torch.LongTensor(y_test).to(device)

print(f"Training data shape: {X_train_tensor.shape}")
print(f"Test data shape: {X_test_tensor.shape}")
print(f"Data device: {X_train_tensor.device}")

# Simple neural network for testing
class TestNetwork(torch.nn.Module):
    def __init__(self, input_size):
        super(TestNetwork, self).__init__()
        self.layers = torch.nn.Sequential(
            torch.nn.Linear(input_size, 256),
            torch.nn.ReLU(),
            torch.nn.BatchNorm1d(256),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(256, 128),
            torch.nn.ReLU(),
            torch.nn.BatchNorm1d(128),
            torch.nn.Dropout(0.3),
            torch.nn.Linear(128, 64),
            torch.nn.ReLU(),
            torch.nn.Linear(64, 2)
        )
    
    def forward(self, x):
        return self.layers(x)

# Create and test model
model = TestNetwork(X_train.shape[1]).to(device)
criterion = torch.nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

print(f"Model device: {next(model.parameters()).device}")
print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

# Training performance test
print("\nTraining performance test...")
start_time = time.time()

model.train()
for epoch in range(10):
    optimizer.zero_grad()
    outputs = model(X_train_tensor)
    loss = criterion(outputs, y_train_tensor)
    loss.backward()
    optimizer.step()
    
    if (epoch + 1) % 5 == 0:
        print(f"Epoch {epoch + 1}/10, Loss: {loss.item():.4f}")

training_time = time.time() - start_time
print(f"Training time: {training_time:.2f} seconds")

# Inference performance test
print("\nInference performance test...")
model.eval()
start_time = time.time()

with torch.no_grad():
    outputs = model(X_test_tensor)
    _, predicted = torch.max(outputs, 1)
    accuracy = (predicted == y_test_tensor).float().mean()

inference_time = time.time() - start_time
print(f"Inference time: {inference_time:.4f} seconds")
print(f"Test accuracy: {accuracy.item():.4f}")

print("\n" + "=" * 60)
print("GRADIENT BOOSTING GPU TEST")
print("=" * 60)

# Test LightGBM GPU support
try:
    import lightgbm as lgb
    print(f"LightGBM version: {lgb.__version__}")
    
    # Create LightGBM dataset
    train_data = lgb.Dataset(X_train, label=y_train)
    
    # GPU parameters
    gpu_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'device': 'gpu' if torch.cuda.is_available() else 'cpu',
        'verbosity': -1,
        'num_leaves': 31,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'min_child_samples': 20,
        'num_boost_round': 100
    }
    
    if torch.cuda.is_available():
        gpu_params['gpu_platform_id'] = 0
        gpu_params['gpu_device_id'] = 0
        gpu_params['max_bin'] = 63
    
    print(f"LightGBM device: {gpu_params['device']}")
    
    # Train LightGBM
    start_time = time.time()
    lgb_model = lgb.train(gpu_params, train_data, valid_sets=[train_data], callbacks=[lgb.early_stopping(10)])
    lgb_training_time = time.time() - start_time
    
    # Predict
    start_time = time.time()
    lgb_pred = lgb_model.predict(X_test)
    lgb_inference_time = time.time() - start_time
    
    lgb_accuracy = ((lgb_pred > 0.5) == y_test).mean()
    
    print(f"LightGBM training time: {lgb_training_time:.2f} seconds")
    print(f"LightGBM inference time: {lgb_inference_time:.4f} seconds")
    print(f"LightGBM accuracy: {lgb_accuracy:.4f}")
    
except ImportError:
    print("LightGBM not available")

# Test CatBoost GPU support
try:
    import catboost as cb
    print(f"\nCatBoost version: {cb.__version__}")
    
    cb_params = {
        'iterations': 100,
        'learning_rate': 0.1,
        'depth': 6,
        'task_type': 'GPU' if torch.cuda.is_available() else 'CPU',
        'verbose': False,
        'random_state': 42
    }
    
    if torch.cuda.is_available():
        cb_params['devices'] = '0'
        cb_params['gpu_ram_part'] = 0.8
    
    print(f"CatBoost task type: {cb_params['task_type']}")
    
    # Train CatBoost
    start_time = time.time()
    cb_model = cb.CatBoostClassifier(**cb_params)
    cb_model.fit(X_train, y_train, verbose=False)
    cb_training_time = time.time() - start_time
    
    # Predict
    start_time = time.time()
    cb_pred = cb_model.predict_proba(X_test)[:, 1]
    cb_inference_time = time.time() - start_time
    
    cb_accuracy = ((cb_pred > 0.5) == y_test).mean()
    
    print(f"CatBoost training time: {cb_training_time:.2f} seconds")
    print(f"CatBoost inference time: {cb_inference_time:.4f} seconds")
    print(f"CatBoost accuracy: {cb_accuracy:.4f}")
    
except ImportError:
    print("CatBoost not available")

print("\n" + "=" * 60)
print("GPU MEMORY USAGE")
print("=" * 60)

if torch.cuda.is_available():
    print(f"GPU memory allocated: {torch.cuda.memory_allocated() / 1024**2:.1f} MB")
    print(f"GPU memory cached: {torch.cuda.memory_reserved() / 1024**2:.1f} MB")
    print(f"GPU memory summary:")
    print(torch.cuda.memory_summary())
else:
    print("GPU not available - using CPU")

print("\n" + "=" * 60)
print("GPU TEST COMPLETED")
print("=" * 60)
