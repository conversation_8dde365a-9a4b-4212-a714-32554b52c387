#!/usr/bin/env python3
"""
Test script to verify LightGBM GOSS/bagging fix
"""

import numpy as np
import pandas as pd
from sklearn.datasets import make_classification
from sklearn.model_selection import StratifiedKFold, cross_val_score
from lightgbm import LGBMClassifier
import optuna

# Create synthetic data
print("Creating synthetic data...")
X, y = make_classification(
    n_samples=1000,
    n_features=10,
    n_informative=8,
    n_redundant=2,
    n_classes=2,
    random_state=42
)

X = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
y = pd.Series(y)

print(f"Data shape: {X.shape}")
print(f"Class distribution: {y.value_counts().to_dict()}")

# Test the fixed LightGBM optimization
def test_lightgbm_optimization():
    """Test LightGBM optimization with GOSS/bagging fix"""
    print("\n" + "="*50)
    print("TESTING LIGHTGBM OPTIMIZATION FIX")
    print("="*50)
    
    cv_folds = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
    
    def objective(trial):
        boosting_type = trial.suggest_categorical('boosting_type', ['gbdt', 'goss'])
        
        params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': boosting_type,
            'num_leaves': trial.suggest_int('num_leaves', 10, 100),
            'learning_rate': trial.suggest_loguniform('learning_rate', 0.01, 0.3),
            'feature_fraction': trial.suggest_uniform('feature_fraction', 0.5, 0.9),
            'min_child_samples': trial.suggest_int('min_child_samples', 5, 50),
            'reg_alpha': trial.suggest_loguniform('reg_alpha', 1e-8, 1.0),
            'reg_lambda': trial.suggest_loguniform('reg_lambda', 1e-8, 1.0),
            'random_state': 42,
            'verbosity': -1
        }
        
        # Add bagging parameters only for gbdt (not compatible with goss)
        if boosting_type == 'gbdt':
            params['bagging_fraction'] = trial.suggest_uniform('bagging_fraction', 0.5, 1.0)
            params['bagging_freq'] = trial.suggest_int('bagging_freq', 1, 5)
            print(f"Trial {trial.number}: GBDT with bagging")
        else:
            print(f"Trial {trial.number}: GOSS without bagging")
        
        try:
            model = LGBMClassifier(**params, n_estimators=50)
            scores = cross_val_score(model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=1)
            score = scores.mean()
            print(f"  Score: {score:.4f}")
            return score
        except Exception as e:
            print(f"  Trial failed with error: {e}")
            return 0.0
    
    # Run optimization
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=10, timeout=120)
    
    print(f"\nBest score: {study.best_value:.6f}")
    print(f"Best params: {study.best_params}")
    
    # Test the best parameters
    print("\nTesting best parameters...")
    best_params = study.best_params.copy()
    
    try:
        best_model = LGBMClassifier(**best_params, n_estimators=100, random_state=42, verbosity=-1)
        final_scores = cross_val_score(best_model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=1)
        print(f"Final CV score: {final_scores.mean():.6f} ± {final_scores.std():.6f}")
        print("✅ LightGBM optimization fix successful!")
        return True
    except Exception as e:
        print(f"❌ Final test failed: {e}")
        return False

# Test individual configurations
def test_individual_configs():
    """Test individual GBDT and GOSS configurations"""
    print("\n" + "="*50)
    print("TESTING INDIVIDUAL CONFIGURATIONS")
    print("="*50)
    
    cv_folds = StratifiedKFold(n_splits=3, shuffle=True, random_state=42)
    
    # Test GBDT with bagging
    print("\n1. Testing GBDT with bagging...")
    gbdt_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'min_child_samples': 20,
        'random_state': 42,
        'verbosity': -1
    }
    
    try:
        gbdt_model = LGBMClassifier(**gbdt_params, n_estimators=50)
        gbdt_scores = cross_val_score(gbdt_model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=1)
        print(f"GBDT score: {gbdt_scores.mean():.6f} ± {gbdt_scores.std():.6f}")
        print("✅ GBDT with bagging works!")
    except Exception as e:
        print(f"❌ GBDT failed: {e}")
    
    # Test GOSS without bagging
    print("\n2. Testing GOSS without bagging...")
    goss_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'goss',
        'num_leaves': 31,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'min_child_samples': 20,
        'random_state': 42,
        'verbosity': -1
    }
    
    try:
        goss_model = LGBMClassifier(**goss_params, n_estimators=50)
        goss_scores = cross_val_score(goss_model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=1)
        print(f"GOSS score: {goss_scores.mean():.6f} ± {goss_scores.std():.6f}")
        print("✅ GOSS without bagging works!")
    except Exception as e:
        print(f"❌ GOSS failed: {e}")
    
    # Test GOSS with bagging (should fail)
    print("\n3. Testing GOSS with bagging (should fail)...")
    goss_bad_params = {
        'objective': 'binary',
        'metric': 'binary_logloss',
        'boosting_type': 'goss',
        'num_leaves': 31,
        'learning_rate': 0.1,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,  # This should cause failure
        'bagging_freq': 5,        # This should cause failure
        'min_child_samples': 20,
        'random_state': 42,
        'verbosity': -1
    }
    
    try:
        goss_bad_model = LGBMClassifier(**goss_bad_params, n_estimators=50)
        goss_bad_scores = cross_val_score(goss_bad_model, X, y, cv=cv_folds, scoring='accuracy', n_jobs=1)
        print(f"❌ GOSS with bagging unexpectedly worked: {goss_bad_scores.mean():.6f}")
    except Exception as e:
        print(f"✅ GOSS with bagging correctly failed: {e}")

if __name__ == "__main__":
    print("LightGBM GOSS/Bagging Compatibility Test")
    print("="*60)
    
    # Test individual configurations first
    test_individual_configs()
    
    # Test the optimization function
    success = test_lightgbm_optimization()
    
    print("\n" + "="*60)
    if success:
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    print("="*60)
