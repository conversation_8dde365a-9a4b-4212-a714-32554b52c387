{"metadata": {"kernelspec": {"language": "python", "display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.13", "mimetype": "text/x-python", "codemirror_mode": {"name": "ipython", "version": 3}, "pygments_lexer": "ipython3", "nbconvert_exporter": "python", "file_extension": ".py"}, "kaggle": {"accelerator": "gpu", "dataSources": [{"sourceId": 91718, "databundleVersionId": 12738969, "sourceType": "competition"}, {"sourceId": 12156348, "sourceType": "datasetVersion", "datasetId": 7474089}, {"sourceId": 480229, "sourceType": "modelInstanceVersion", "modelInstanceId": 385433, "modelId": 404652}], "dockerImageVersionId": 31090, "isInternetEnabled": true, "language": "python", "sourceType": "notebook", "isGpuEnabled": true}}, "nbformat_minor": 4, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": "# <p style=\"border-radius: 5px; color: white; font-weight: bold; font-size: 100%; text-align: center; background-color:#314a6e;\"><br> Advanced Introvert Prediction - Enhanced Ensemble <br> <u>PS - S05E07 - Target: 0.977+</u> <br> <br> </p>\n\n## Advanced Strategies Implementation\n\n**Target Accuracy: 0.977327+**\n\n### Phase 1: Quick Wins\n1. **Optuna Hyperparameter Optimization** - Systematic Bayesian optimization for all models\n2. **Advanced Stacking Ensemble** - Meta-learner with cross-validation based predictions  \n3. **Enhanced Feature Engineering** - Interaction features, statistical features, clustering-based features\n\n### Phase 2: Architecture Improvements\n4. **CatBoost & TabNet Models** - Advanced algorithms for tabular data\n5. **Stratified Stacking** - Multi-level ensemble with reduced bias\n6. **Scientific Blending** - Optimized ensemble weights using scipy\n\n### Phase 3: Advanced Techniques\n7. **Pseudo-labeling** - Semi-supervised learning with original dataset\n8. **Neural Architecture Search** - Enhanced ANN with residual connections\n9. **Robust Cross-Validation** - Stratified K-fold with nested CV\n\n**Previous Best Results:**\n- Logistic Regression: 0.974089\n- K-Nearest Neighbours: 0.974089  \n- XGBoost: 0.974089\n- ANN: 0.975708\n- **Current Ensemble: 0.976518**", "metadata": {}}, {"cell_type": "code", "source": "# Enhanced imports for advanced strategies\nimport numpy as np\nimport pandas as pd\nimport matplotlib.pyplot as plt\nimport seaborn as sns\nfrom scipy.optimize import minimize\nfrom scipy import stats\nfrom itertools import combinations\n\n# Core ML libraries\nfrom sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split\nfrom sklearn.metrics import accuracy_score, classification_report, roc_auc_score\nfrom sklearn.preprocessing import StandardScaler, PolynomialFeatures\nfrom sklearn.cluster import KMeans\nfrom sklearn.ensemble import StackingClassifier\nfrom sklearn.calibration import CalibratedClassifierCV\n\n# Models\nfrom sklearn.linear_model import LogisticRegression, Ridge\nfrom sklearn.neighbors import KNeighborsClassifier\nfrom sklearn.ensemble import RandomForestClassifier\nimport xgboost as xgb\nfrom lightgbm import LGBMClassifier, log_evaluation, early_stopping\nfrom catboost import CatBoostClassifier\n\n# Neural Networks\nimport torch\nimport torch.nn as nn\nimport torch.nn.functional as F\nfrom torch.utils.data import Dataset, DataLoader\nimport torch.optim as optim\n\n# Advanced techniques\nfrom sklearn.experimental import enable_iterative_imputer\nfrom sklearn.impute import IterativeImputer\nimport optuna\nfrom sklearn.model_selection import cross_validate\n\n# Suppress warnings\nimport warnings\nwarnings.filterwarnings('ignore')\n\n# Set random seeds for reproducibility\nnp.random.seed(42)\ntorch.manual_seed(42)\nif torch.cuda.is_available():\n    torch.cuda.manual_seed(42)\n\nprint(\"Enhanced libraries loaded successfully!\")", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:49.608640Z", "iopub.execute_input": "2025-07-31T09:56:49.608934Z", "iopub.status.idle": "2025-07-31T09:56:59.508946Z", "shell.execute_reply.started": "2025-07-31T09:56:49.608907Z", "shell.execute_reply": "2025-07-31T09:56:59.508082Z"}}, "outputs": [{"name": "stdout", "text": "Enhanced libraries loaded successfully!\n", "output_type": "stream"}], "execution_count": 1}, {"cell_type": "code", "source": "class Config:\n    \"\"\"Enhanced configuration with advanced parameters\"\"\"\n    # Paths\n    train_path = \"train.csv\"\n    test_path = \"test.csv\" \n    sam_sub_path = \"sample_submission.csv\"\n    original_path = \"personality_datasert.csv\"\n    \n    # Cross-validation settings\n    N_FOLDS = 10\n    RANDOM_STATE = 42\n    \n    # Neural network settings\n    EPOCHS = 300\n    BATCH_SIZE = 128\n    LEARNING_RATE = 0.001\n    \n    # Optimization settings\n    OPTUNA_TRIALS = 100\n    OPTUNA_TIMEOUT = 3600  # 1 hour\n    \n    # Ensemble settings\n    ENSEMBLE_LEVELS = 3\n    BLEND_OPTIMIZATION = True\n    \n    # Feature engineering\n    POLY_DEGREE = 2\n    N_CLUSTERS = 5\n    \n    # Target accuracy\n    TARGET_ACCURACY = 0.977327\n    \nconfig = Config()\nprint(f\"Configuration loaded. Target accuracy: {config.TARGET_ACCURACY}\")\nprint(f\"Cross-validation folds: {config.N_FOLDS}\")\nprint(f\"Optuna trials: {config.OPTUNA_TRIALS}\")", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.510759Z", "iopub.execute_input": "2025-07-31T09:56:59.511242Z", "iopub.status.idle": "2025-07-31T09:56:59.515479Z", "shell.execute_reply.started": "2025-07-31T09:56:59.511223Z", "shell.execute_reply": "2025-07-31T09:56:59.514778Z"}}, "outputs": [{"name": "stdout", "text": "Configuration loaded. Target accuracy: 0.977327\nCross-validation folds: 10\nOptuna trials: 100\n", "output_type": "stream"}], "execution_count": 2}, {"cell_type": "code", "source": "train = pd.read_csv(vars.train_path, index_col='id')\ntest = pd.read_csv(vars.test_path, index_col='id')\noriginal = pd.read_csv(vars.original_path)\nsam_sub = pd.read_csv(vars.sam_sub_path)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.516238Z", "iopub.execute_input": "2025-07-31T09:56:59.516490Z", "iopub.status.idle": "2025-07-31T09:56:59.612935Z", "shell.execute_reply.started": "2025-07-31T09:56:59.516466Z", "shell.execute_reply": "2025-07-31T09:56:59.612026Z"}}, "outputs": [], "execution_count": 3}, {"cell_type": "code", "source": "off_pred_probs = pd.DataFrame({'Index': range(1, train.shape[0]+1)})\ntest_pred_probs = pd.DataFrame({'Index': range(1, test.shape[0]+1)})", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.613781Z", "iopub.execute_input": "2025-07-31T09:56:59.614033Z", "iopub.status.idle": "2025-07-31T09:56:59.619109Z", "shell.execute_reply.started": "2025-07-31T09:56:59.614009Z", "shell.execute_reply": "2025-07-31T09:56:59.618368Z"}}, "outputs": [], "execution_count": 4}, {"cell_type": "markdown", "source": "<h2><center>Exploratory Data Analysis </center></h2>\n\n1. Check if there are any outliers or anomolies in the dataset\n2. Check if the dataset is balanced\n3. Deal with missing data\n\n<br>\n\n#### Understanding the dataset\n\nWhat does the columns say\n\n| | | |\n|------|----|---|\n|1.| **Time spent alone** | Number of hours an individual typically spends alone daily |\n|2.| **Stage fear** | Whether the person experiences stage fear. |\n|3.| **Social Event Attendance** | Frequency (scale 0-10) of attending social events. |\n|4.| **Going Outside** | How often the individual goes outside (scale 0-10). |\n|5.| **Drained After Socilizing** | Whether the individual feels drained after socializing. |\n|6.| **Friends Circle Size** | feels drained after socializing. |\n|7.| **Post Frequence** | Frequency of posting on social media. |", "metadata": {}}, {"cell_type": "code", "source": "train.shape, test.shape", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.620116Z", "iopub.execute_input": "2025-07-31T09:56:59.620364Z", "iopub.status.idle": "2025-07-31T09:56:59.639895Z", "shell.execute_reply.started": "2025-07-31T09:56:59.620342Z", "shell.execute_reply": "2025-07-31T09:56:59.639161Z"}}, "outputs": [{"execution_count": 5, "output_type": "execute_result", "data": {"text/plain": "((18524, 8), (6175, 7))"}, "metadata": {}}], "execution_count": 5}, {"cell_type": "code", "source": "cols = train.columns\nvals = train.isna().sum()\nper = train.isna().sum()*100/train.shape[0]\nprint(\"\\033[1mPercentage of Missing Values\\033[0m\".center(50))\nprint(pd.DataFrame({\"Count\": vals, 'Percentage': per.round(2)}))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.640723Z", "iopub.execute_input": "2025-07-31T09:56:59.641073Z", "iopub.status.idle": "2025-07-31T09:56:59.670943Z", "shell.execute_reply.started": "2025-07-31T09:56:59.641046Z", "shell.execute_reply": "2025-07-31T09:56:59.670233Z"}}, "outputs": [{"name": "stdout", "text": "       \u001b[1mPercentage of Missing Values\u001b[0m       \n                           Count  Percentage\nTime_spent_Alone            1190        6.42\nStage_fear                  1893       10.22\nSocial_event_attendance     1180        6.37\nGoing_outside               1466        7.91\nDrained_after_socializing   1149        6.20\nFriends_circle_size         1054        5.69\nPost_frequency              1264        6.82\nPersonality                    0        0.00\n", "output_type": "stream"}], "execution_count": 6}, {"cell_type": "code", "source": "cols = test.columns\nvals = test.isna().sum()\nper = test.isna().sum()*100/test.shape[0]\nprint(\"\\033[1mPercentage of Missing Values\\033[0m\".center(50))\nprint(pd.DataFrame({\"Count\": vals, 'Percentage': per.round(2)}))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.673804Z", "iopub.execute_input": "2025-07-31T09:56:59.674073Z", "iopub.status.idle": "2025-07-31T09:56:59.686126Z", "shell.execute_reply.started": "2025-07-31T09:56:59.674051Z", "shell.execute_reply": "2025-07-31T09:56:59.685274Z"}}, "outputs": [{"name": "stdout", "text": "       \u001b[1mPercentage of Missing Values\u001b[0m       \n                           Count  Percentage\nTime_spent_Alone             425        6.88\nStage_fear                   598        9.68\nSocial_event_attendance      397        6.43\nGoing_outside                466        7.55\nDrained_after_socializing    432        7.00\nFriends_circle_size          350        5.67\nPost_frequency               408        6.61\n", "output_type": "stream"}], "execution_count": 7}, {"cell_type": "code", "source": "cols = original.columns\nvals = original.isna().sum()\nper = original.isna().sum()*100/test.shape[0]\nprint(\"\\033[1mPercentage of Missing Values\\033[0m\".center(50))\nprint(pd.DataFrame({\"Count\": vals, 'Percentage': per.round(2)}))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.686911Z", "iopub.execute_input": "2025-07-31T09:56:59.687238Z", "iopub.status.idle": "2025-07-31T09:56:59.707168Z", "shell.execute_reply.started": "2025-07-31T09:56:59.687211Z", "shell.execute_reply": "2025-07-31T09:56:59.706388Z"}}, "outputs": [{"name": "stdout", "text": "       \u001b[1mPercentage of Missing Values\u001b[0m       \n                           Count  Percentage\nTime_spent_Alone              63        1.02\nStage_fear                    73        1.18\nSocial_event_attendance       62        1.00\nGoing_outside                 66        1.07\nDrained_after_socializing     52        0.84\nFriends_circle_size           77        1.25\nPost_frequency                65        1.05\nPersonality                    0        0.00\n", "output_type": "stream"}], "execution_count": 8}, {"cell_type": "code", "source": "# # too much data loss, need to impute\n# train.dropna(inplace = True)\n# test.dropna(inplace = True)\n# train.shape, test.shape\n# # ((10189, 9), (3397, 8))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.708021Z", "iopub.execute_input": "2025-07-31T09:56:59.708342Z", "iopub.status.idle": "2025-07-31T09:56:59.721758Z", "shell.execute_reply.started": "2025-07-31T09:56:59.708319Z", "shell.execute_reply": "2025-07-31T09:56:59.721011Z"}}, "outputs": [], "execution_count": 9}, {"cell_type": "markdown", "source": "The missing data percentage is small, but by collectively removing each observation with missing value, we're losing 30% data causing data loss.\n\nActual Train and Test dataset shapes are : <br>\n**((18524, 9), (6175, 8))**\n\nAfter dropping missing observations Train and Test dataset shapes are : <br>\n**((10189, 9), (3397, 8))**", "metadata": {}}, {"cell_type": "code", "source": "cont_vars = train.select_dtypes(include = \"float64\").columns.to_list()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.722465Z", "iopub.execute_input": "2025-07-31T09:56:59.722687Z", "iopub.status.idle": "2025-07-31T09:56:59.737332Z", "shell.execute_reply.started": "2025-07-31T09:56:59.722670Z", "shell.execute_reply": "2025-07-31T09:56:59.736636Z"}}, "outputs": [], "execution_count": 10}, {"cell_type": "code", "source": "fig, axes = plt.subplots(nrows = 1, ncols = 5, figsize=(20, 6));\nfig.suptitle(\"Dist. of Variables in Train Data\", fontsize = 20);\nvals = list(range(5)) # , ax = axes[next(index)]\nindex = iter(vals);\nfor i in cont_vars:\n    sns.distplot(train[i], ax = axes[next(index)]);\nplt.tight_layout();\nfig.show()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:56:59.738111Z", "iopub.execute_input": "2025-07-31T09:56:59.738364Z", "iopub.status.idle": "2025-07-31T09:57:01.413912Z", "shell.execute_reply.started": "2025-07-31T09:56:59.738342Z", "shell.execute_reply": "2025-07-31T09:57:01.412894Z"}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 2000x600 with 5 Axes>", "image/png": "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***************************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\n"}, "metadata": {}}], "execution_count": 11}, {"cell_type": "code", "source": "fig, axes = plt.subplots(nrows = 1, ncols = 5, figsize=(20, 6))\nfig.suptitle(\"Dist. of Variables in Test Data\", fontsize = 20)\nvals = list(range(5))\nindex = iter(vals)\nfor i in cont_vars:\n    sns.distplot(test[i], ax = axes[next(index)])\nplt.tight_layout()\nplt.show()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:01.414824Z", "iopub.execute_input": "2025-07-31T09:57:01.415182Z", "iopub.status.idle": "2025-07-31T09:57:02.577458Z", "shell.execute_reply.started": "2025-07-31T09:57:01.415149Z", "shell.execute_reply": "2025-07-31T09:57:02.576630Z"}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 2000x600 with 5 Axes>", "image/png": "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\n"}, "metadata": {}}], "execution_count": 12}, {"cell_type": "code", "source": "fig, axes = plt.subplots(nrows = 1, ncols = 5, figsize=(20, 6))\nfig.suptitle(\"Dist. of Variables in Original Data\", fontsize = 20)\nvals = list(range(5))\nindex = iter(vals)\nfor i in cont_vars:\n    sns.distplot(original[i], ax = axes[next(index)])\nplt.tight_layout()\nplt.show()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:02.578392Z", "iopub.execute_input": "2025-07-31T09:57:02.578685Z", "iopub.status.idle": "2025-07-31T09:57:03.589987Z", "shell.execute_reply.started": "2025-07-31T09:57:02.578662Z", "shell.execute_reply": "2025-07-31T09:57:03.589139Z"}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": "<Figure size 2000x600 with 5 Axes>", "image/png": "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*********************************/TZ89r5/FCfX945MVPAtDttXbLjVdffbXe93/961/15ptvKi0tTXPnzpXNZtOKFSv02GOP6YYbbpAkvfTSS4qMjNQ777yj2267reMvCl3CazsyWzX+jvh+HRQJAAAAgOaYS2o/wBwZYpLRYHBxNO03oFegTp89rxOF5RrXL8zV4QC4iJSUFM2bN08TJkzQpEmTtGLFCpWVlTneq5o7d6769OmjJUuWSJKeeeYZLVq0SK+99pri4uIcxYBBQUEKCgqSwWDQ/fffr6efflpDhgzRgAED9PjjjysmJkazZs1y1WUCgFtyaaV4e9qNtERycrL+9a9/aevWrerbt2+754NUVWPVxl2n9Pddp1VuqVHvIJPuiu+vn101sNMS4lJttfgVg8IlSTuOnVG11dppjw3ANZyx5UZ5ebmqqqoc22kcP35cZrO53pyhoaGKj49vcs7KykqVlJTUOwAAXZOzt9y4++67ZTAY6h0zZ87s6MsAAAAXMNe1To8K7d77idvF9qxtAX/6bLmLIwHQEnPmzFFqaqoWLVqksWPHau/evdq0aZOjG25mZqZycnIc41etWiWLxaKbb75Z0dHRjiM1NdUx5pFHHtH//u//6t5779XEiRNVWlqqTZs2tWvfcQBAQy5Nire13cjF2Gw2JScn6+2339ZHH32kAQMGOCNcj3feUqO/fXJc+04Xy2iQEi6J1C+uHqIRMSEyuOCTuSP7hCjEz1vnKqu1P6u40x8fQOdyxpYbjz76qGJiYhxJcPt5rZlzyZIlCg0NdRyxsbGtvRQAQCfoiC03JGnmzJnKyclxHK+//npnXA4AAKhjrxSPCnWPZJE9KW4urpClmqIPoDtITk7WyZMnVVlZqR07dig+Pt5xX3p6utatW+f4/sSJE7LZbA2OJ554wjHGYDDoySeflNlsVkVFhbZs2aKhQ4d24hUBgGdwaVJcqm03smbNGq1fv16HDh3SggULGrQbWbhwoWO8xWLR3r17tXfvXlksFmVlZWnv3r06evSoY8zPf/5zvfLKK3rttdcUHBwss9kss9ms8+fPd/r1uYuqGqte+vSEMs+Uy9/HSz+ZMkDfHx4hL6Pr2lR5G42aOKC22nP3ybMuiwNA97B06VJt2LBBb7/9drs+abtw4UIVFxc7jlOnTjkxSgCAs1y45caIESO0evVqBQQEaO3atY2Of/XVV3Xfffdp7NixGj58uP761786PrB7IZPJpKioKMcRFkabUwAAOpOjUjzEPZLiof4+CvHzlk1SVhHvXQIAAHQUlyfFW9tuJDs7W+PGjdO4ceOUk5Oj1NRUjRs3Tvfcc49jzKpVq1RcXKzp06fXa0mycePGTr8+d2Cz2fTmntM6WVguPx+j5k8dqIHhQa4OS5J0WWztm5DH8stUVG5xcTQAOlJ7ttxITU3V0qVLtXnzZo0ePdpxu/281sxpMpkUEhJS7wAAdC0dseWGXXp6uiIiIjRs2DAtWLBAhYWFTc7BlhsAADjXeUuNis5XSXKfpLgk9Q2jhToAAEBHc3lSXGpdu5G4uLhG242kp6c7xjR2v81m09133915F+VG9p0udrRMvyu+f5dqTxUW6KsB4YGySdp7qsjV4QDoQG3dcuPZZ5/VU089pU2bNmnChAn17hswYICioqLqzVlSUqIdO3a0axsPAIBrdcSWG1Jt6/SXXnpJaWlpeuaZZ7Rt2zZdc801qqmpaXQOttwAAMC57K3Te/j7yN/Xy8XROI+9hfqpMyTFAQAAOoq3qwNA13auokrvfpEtSfre8AgN7N01KsQvdFm/HjpeUKY9mUWaNrS3S/Y3B9A5UlJSNG/ePE2YMEGTJk3SihUrGmy50adPHy1ZskSS9Mwzz2jRokV67bXXFBcX50iEBAUFKSgoSAaDQffff7+efvppDRkyRAMGDNDjjz+umJgYzZo1y1WXCQBwMfuWG+np6fW23LjtttscX48aNUqjR4/WoEGDlJ6erquvvrrBPAsXLlRKSorj+5KSEhLjAAC0g7vtJ27XN8xfknT6LO3TAQAAOgpJcTTro6/ydL6qRjE9/DR9aISrw2nUyJhQ/d/ebBWUVir3XKVbtc8CUN+cOXOUn5+vRYsWyWw2a+zYsQ223DAav22CsmrVKlksFt1888315lm8eLGeeOIJSdIjjzyisrIy3XvvvSoqKtKUKVO0adOmdu07DgBwLWdsubFly5Z6W240ZuDAgQoPD9fRo0cbTYqbTCaZTKbWXwAAAGhUXl1SPCLYvV6v9e3hL4OkovNVOldRpWA/H1eHBAAA4HZIiqNJZ8os+uzEGUnStSOj5WXsmhXYJh8vDY4I0lfmczqYXUxSHHBzycnJSk5ObvS+C7fSkKQTJ05cdD6DwaAnn3xSTz75pBOiAwB0BRduuWHv/GHfcqOpNUSq3XLjd7/7nT744IMGW2405vTp0yosLFR0dLSzQgcAAM3IP1cpSYoIdq8PnZl8vNQryFcFpRblFFeQFAcAAOgAXWJPcXRNW7/Kk9UmDY4I6pJt0y90aUyIJOlAdomLIwEAAEBXkJKSojVr1mj9+vU6dOiQFixY0GDLjYULFzrGP/PMM3r88ce1du1ax5YbZrNZpaWlkqTS0lI9/PDD+vTTT3XixAmlpaXphhtu0ODBg5WYmOiSawQAwNPkl9YmxXu7WVJckqJDa1uo5xTRQh0AAKAjUCmORpVVVuuL00WSpIThXbNt+oWGR4XIaMhSTnGFzpRZ1DPQ19UhAQAAwIWcveWGl5eX9u3bp/Xr16uoqEgxMTGaMWOGnnrqKVqkAwDQCSqqanSuolqSeybFY3r468usYmUXV7g6FAAAALdEUhyN2pN5VtVWm2J6+Cm2Z4Crw7moQJO34sIDdSy/TAdzSjRlcLirQwIAAICLOXPLDX9/f33wwQdOigwAALRWXl3r9BA/b/n5eLk4GueLCa3dDjCbSnEAAIAOQVIcDVhtNu08XruXeHxcLxkMXXMv8e8aHhWiY/llOpJ7jqQ4AAAAAHRDr+3IbPccd8T3c0IkALoa+37i4W5YJS5J0T1q26cXlllUWVUjkxsm/gEAAFyJPcXRwMnCchWWWWTyNmp0bKirw2mxoRG1+54fLyiTpdrq4mgAAAAAAADgLPnnatuKR7hpUjzI5K0Qv9r6JXMJLdQBAACcjaQ4GtifXSxJujQmRCbv7vOp1N7BJvXw91G11abjBWWuDgcAAAAAAABOYm+f3jvIPZPikhQdWlstTgt1AAAA5yMpjnqsNpsOZpdIki6N6T5V4pJkMBg0JDJYkvR13jkXRwMAAAAAAABnsbdP7x3s5+JIOk50j9pro1IcAADA+UiKo56ss+dVfL5Kvl5GDa5rR96dDI2sjflILklxAAAAAAAAd1BjtelsuUVSbadAdxUVUpcULyYpDgAA4GwkxVHPgboq8WFRwfLx6n5Pj0G9g2SQVFBqUfH5KleHAwAAAAAAgHYqKrfIapN8vAwKrtt32x1F1iXFc89VymazuTgaAAAA99L9sp7oUF/XVViPiA5xcSRt4+fjpZgetfsvHS8odXE0AAAAAAAAaK/Cstoq8Z6BvjIaDC6OpuOEB5nkZTDIUm1VEcUeAAAATkVSHA6lldWOPYsGdcPW6XYDwwMlScfyy1wcCQAAAAAAANqroLR2P/Fege7bOl2SvIwGR3v4XPYVBwAAcCqS4nA4ll9bWR0V4qcgU/dtRTWgd21S/HgBSXEAAAAAAIDuzl4p3ivQ18WRdLyIkLqkOPuKAwAAOBVJcTh8U1dZPaguqdxdxfUKlEG1L5jYVxwAAAAAAKB7O1NalxQPcu9Kcam2WEWq3VccAAAAzkNSHA72SvFBvbtv63Spdl/xPmG1+4rbrwkAAAAAAADdU2FZXfv0IPevFI+0J8Vpnw4AAOBUJMUhSSoqt6iwzCKjQYoL796V4pLUv2eAJCnzTLmLIwEAAAAAAEBb1VhtOltW2wnQE9qn25PieecqVWO1uTgaAAAA90FSHJK+TR5Hh/rLz8fLxdG0X/9etYn9k4UkxQEAAAAAALqr4vNVqrHZ5G00KMTfx9XhdLgeAT7y8TLUfhig3OLqcAAAANwGSXFIkk6fPS9J6lvXdry769ertlI8t6RCFVU1Lo4GAAAAAAAAbVFYWts6PSzQV0aDwcXRdDyjwaDwur3T89lXHAAAwGm8XR0AuoZTZ2srqmPr2o53dyF+PgoL8NHZ8iqdOlOuIZHBrg4JAAAAALq813Zktuv8O+L7OSkSAKhVWFZbLe0JrdPtegeblFNcofxzlbok2tXRAAAAuAcqxaEaq03ZRe5VKS5d0EKdfcUBAAAAAAC6JXsL8TBPSorbK8VLqRQHAABwFpLiUG5JhapqbPLzMTraM7mD/nUt1E8Wlrk4EgAAAAAAALTF2bpK8Z4BHpQUD6Z9OgAAgLORFMe3+4n3CHCrvZn61bWCP332vKw2m4ujAQAAAAAAQGudLa+SJIV5aFLcxntaAAAATkFSHDpdt594357u0zpdkiKC/eRtNKiy2qozpRZXhwMAAAAAALqglStXKi4uTn5+foqPj9fOnTubHf+Pf/xDw4cPl5+fn0aNGqX333+/3v2lpaVKTk5W37595e/vrxEjRmj16tUdeQlu7dv26T4ujqTz9Ao0ySDpfFWNyiw1rg4HAADALZAUh3KKKyRJMaHulRT3MhoU06P2mk7X7ZkOAAAAAABgt3HjRqWkpGjx4sXas2ePxowZo8TEROXl5TU6fvv27br99tv105/+VJ9//rlmzZqlWbNmaf/+/Y4xKSkp2rRpk1555RUdOnRI999/v5KTk/Xuu+921mW5jdLKapXXJYU9qVLc19uoHgG1HwKghToAAIBzkBT3cFabTbkltUnx6FA/F0fjfH3qkuJZddXwAAAAAAAAdsuXL9f8+fOVlJTkqOgOCAjQ2rVrGx3/3HPPaebMmXr44Yd1ySWX6KmnntJll12mP//5z44x27dv17x58zR9+nTFxcXp3nvv1ZgxYy5agY6GTp2pfT/H38dLfj5eLo6mc9lbqBeQFAcAAHAKkuIerrDUomqrTT5eBoUFut8nbvuE1SXFqRQHAAAAAAAXsFgs2r17txISEhy3GY1GJSQkKCMjo9FzMjIy6o2XpMTExHrjr7jiCr377rvKysqSzWbT1q1b9fXXX2vGjBlNxlJZWamSkpJ6B6TTZ2vfz/Gk1ul2vYPq9hUvJSkOAADgDCTFPZy5rko8MsRPRoPBxdE4n71SPLuoQlabzcXRAAAAAACArqKgoEA1NTWKjIysd3tkZKTMZnOj55jN5ouOf/755zVixAj17dtXvr6+mjlzplauXKmrrrqqyViWLFmi0NBQxxEbG9uOK3Mf9kpxT2qdbhdeVylO+3QAAADnICnu4czFtZ+4jQpxv9bpUm2rKV8voyw1Vl5EAAAAAACADvf888/r008/1bvvvqvdu3frD3/4g37+859ry5YtTZ6zcOFCFRcXO45Tp051YsRd16m67fB6emBS3N4+nUpxAAAA5/B2dQBwLXNJ7X+so9xwP3FJMhoMiunhrxOFZco6e16Rbpr8BwAAAAAArRMeHi4vLy/l5ubWuz03N1dRUVGNnhMVFdXs+PPnz+vXv/613n77bV133XWSpNGjR2vv3r1KTU1t0HrdzmQyyWQytfeS3M637dM9MCle1z79bJlFVTVW+XhR2wQAANAe/G/Kw7l7pbgk9a3bV/w0+4oDAAAAAIA6vr6+Gj9+vNLS0hy3Wa1WpaWlafLkyY2eM3ny5HrjJenDDz90jK+qqlJVVZWMxvpvuXl5eclqtTr5Ctzft+3TPW9P8SCTt/x8jLJJKiy1uDocAACAbo9KcQ9WWVWjs+VVktw7KW7fVzyrruUWAAAAAACAJKWkpGjevHmaMGGCJk2apBUrVqisrExJSUmSpLlz56pPnz5asmSJJOmXv/ylpk2bpj/84Q+67rrrtGHDBu3atUsvvPCCJCkkJETTpk3Tww8/LH9/f/Xv31/btm3TSy+9pOXLl7vsOrur7LoChx4e2D7dYDCod5BJp86eV35ppdt2eQQAAOgsJMU9WF7dHtvBJm8FmNz3qdCnrlI8p7hCNVabvIwGF0cEAAAAAAC6gjlz5ig/P1+LFi2S2WzW2LFjtWnTJkVGRkqSMjMz61V9X3HFFXrttdf02GOP6de//rWGDBmid955RyNHjnSM2bBhgxYuXKg777xTZ86cUf/+/fW73/1O//M//9Pp19edlVVWq6SiWpIU6u95leKS1DvYrzYpfq5CUqirwwEAAOjW3DcTiosqKK1NiocHu/eeVT0DfeXnY1RFlVW5JRWKqascBwAAAAAASE5OVnJycqP3paenN7jtlltu0S233NLkfFFRUXrxxRedFZ7Hyqnb8s/kbZSfj5eLo3GN3nXv2eXXFbYAAACg7dhT3IPl1yXFewe5d1LcaDA4EuFZ7CsOAAAAAADQ5eUUV0jy3Cpx6dv37Ozv4QEAAKDtSIp7sIJznlEpLkl9ewRIkrLOkhQHAAAAAADo6nKKSIrbK8ULzllks9lcHA0AAED3RlLcg31bKe7r4kg6nn1fcSrFAQAAAAAAur7suvbpnpwUDwv0kdEgWWqsOle3vzoAAADahqS4h7LabCostUiSwt28fbokxYT6SZJySypUY+WTtQAAAAAAAF2Zo1I8wHOT4t5Go8ICaotZCmihDgAA0C4kxT1UUXmVqq02eRkNCgt0/0rxsEBf+XobVW218SICAAAAAACgi7NXivfw4Epx6dtiloK64hYAAAC0DUlxD2VPDPcK9JXRYHBxNB3PaDAoKqS2WjynuMLF0QAAAAAAAKA55mL7nuLuX8zRnPAgKsUBAACcgaS4h8o/V7efeLD7t063i6proW4uZl9xAAAAAACArizHkRT37ErxXnWV4oUkxQEAANrF29UBwDXsny71hP3E7aJDqRQHAAAAADTvtR2Z7Tr/jvh+TooE8FwlFVUqrayWRFKc9ukAAADOQaW4hzpTVvsf6V4esJ+4XXSov6Rv228BAAAAAACg68kp+rZK3Nfbs9++tLdPP1NmUY3V5uJoAAAAui/P/l+lB7MnxXt6UFI8KsRPBknnKqt1rqLK1eEAAAAAAACgEdl1W9/Zu/55shB/H3kbDaqx2VRUTrU4AABAW5EU90A1VpuKymuTwp6UFPf1Njqul2pxAAAAAACArsn+vk1MD38XR+J6RoOBFuoAAABOQFLcA5lLKlRjs8nLYFCIh+3LFF33Yop9xQEAAAAAALqmnCIqxS/Uq66FekFppYsjAQAA6L5IinugzMJySVKPAB8ZDQYXR9O57C+mzCUkxQEAAAAAALqibCrF6/m2UpykOAAAQFt1iaT4ypUrFRcXJz8/P8XHx2vnzp1Njj1w4IBuuukmxcXFyWAwaMWKFe2e09OcOlObFPek1ul20SG1SfGcur2pAAAAAAAA0LXY37eJCqFSXJLC6yrFC8tonw4AANBWLk+Kb9y4USkpKVq8eLH27NmjMWPGKDExUXl5eY2OLy8v18CBA7V06VJFRUU5ZU5Pk+nBSfGoukrx/HOVqqiqcXE0AAAAAAAA+K6cotpK8egeJMUlKsUBAACcweVJ8eXLl2v+/PlKSkrSiBEjtHr1agUEBGjt2rWNjp84caKWLVum2267TSaTySlzehpPToqH+vvI38dLVpt0NK/U1eEAAAAAAADgAjabTTn29umhtE+XpF51SfHi8iqKPAAAANrIpUlxi8Wi3bt3KyEhwXGb0WhUQkKCMjIyOm3OyspKlZSU1DvcmScnxQ0Gg2Nf8YM57v3vDAAAAAAA0N0Un6/S+brEr73jn6cL9PWSn49RNkknC8tdHQ4AAEC35NKkeEFBgWpqahQZGVnv9sjISJnN5k6bc8mSJQoNDXUcsbGxbXrs7sKT9xSX5EiKHyIpDgAAAAAA0KVk17VO7xXoKz8fLxdH0zUYDAZHC/XjBXQ+BAAAaAuXt0/vChYuXKji4mLHcerUKVeH1GFKK6tVWGaRJIUFeGZSPKqu9RZJcQAAAAAAgK4lp/i8JPYT/y57UvxYQZmLIwEAAOievF354OHh4fLy8lJubm6923NzcxUVFdVpc5pMpib3J3c32UW1Lyz8fbw89tO29tZbX5nPyWazyWAwuDgiAAAAAAAASFJ23X7iUSHsJ36hXkG1xS0nSIoDAAC0iUsrxX19fTV+/HilpaU5brNarUpLS9PkyZO7zJzuJOtsbVK8R4CPiyNxnYhgkwySisqrlHeu0tXhAAAAAAAAoI65rlI8hkrxer5tn05SHAAAoC1cWikuSSkpKZo3b54mTJigSZMmacWKFSorK1NSUpIkae7cuerTp4+WLFkiSbJYLDp48KDj66ysLO3du1dBQUEaPHhwi+b0ZFl1leI9/D03Ke7jZVR4sEn55yp1KKdEkSG8yAIAAAAAAOgKcur2FI8OpVL8QiTFAcD5qq1WFZZadK6iWtU1Vm37Ol+9g0waEB4of1/P7LQLuDOXJ8XnzJmj/Px8LVq0SGazWWPHjtWmTZsUGRkpScrMzJTR+G1Be3Z2tsaNG+f4PjU1VampqZo2bZrS09NbNKcns7dPD/XQ/cTtokL8lH+uUl+Zz2n6sAhXhwMAAAAAAABJ2VSKNyo8sPa9vIJSi4rPVynUgwteAKA9zpZbtPdUkb7KKVF2UYVqbDbHfS99elKS5G00aGxsD900vq9uGBujAF+Xp9IAOEGX+E1OTk5WcnJyo/fZE912cXFxsl3wR6otc3qybCrFJUnRoX76MqtYX+WUuDoUAAAAAAAA1Mktqd3qjs5+9Zl8vBTs561zFdU6UVCmMbE9XB0SAHQreSUV+vBQrg5ml+jCDJPJ26hQfx/5ehsV7Ocjc/F5nS2v0q6TZ7Xr5Fk9n3ZET94wUgkjKLoEursukRRH58mua0HlyXuKS7WV4pL0lfmciyMBAAAAAACAXV5J7XtXJMUb6hVo0rmKah0nKQ4ALVZVY9WWg7n65GiBIxk+qHegRvfpoUERQQoL8JHBYJAk3RHfTzabTafPnte/9+do/faTyio6r3te2qWUHwzV/35/sGMsgO6HpLiHYU/xWpGhtS+sjuaVylJtla+38SJnAAAAAAAAoCOVVlarzFIjSYoINrk4mq4nPMhXJwrL2FccAFqosLRSr+w46ehCckl0iGaMiGz2g1cGg0GxPQN071WD9OPL45S6+bD+9slxLf/wa1mqrXoocVhnhQ/AycgEepDqGqvMJfZKcc/eU7yHv4+CTd6qttp0rKDU1eEAAAAAAAB4PHuVeJDJW4Emanm+Kzyo9oMCJMUB4OJOFJTpL+nfKLekUkEmb/348v768eX9W9WJxN/XS4//cIQW/2iEJOnPW49q0/6cjgoZQAcjKe5B8s5VqsZqk4+XQUF+nv3CwmAwaHh0sCTpqxxaqAMAAAAAALha3rnaSj6qxBtHUhwAWuZoXqle3H5c56tq1DfMX8nfG6xLokPaPF/SlQM0f+oASdJD/9jn6MgLoHshKe5Bsuv+UEeF+snIvhcaHlW7CB4yl7g4EgAAAAAAAOTWVYr3JineqPCg2s6PxwvKZLPZLjIaADzTqTPlevnTE6qqsWloZJDmTx2oECdsJ/vIzOG6rF8PlVZW65l/f+WESAF0Ns8uF/Yw9k8vxYT6uziSroFKcQBAZ3ltR2arxt8R36+DIgEAAAC6rvy6SvHWtLb1JD0DfWU01O69nl9aqYhgfk4AcKHC0kqtz/g2IX5XfH95ezmnNtTHy6gnbxipH/35E737RbbmXdFf4/v3dMrcADoHleIexJ4U79ODpLgkDY+qS4pTKQ4AAAAAAOBytE9vnreXUX3Cat/XO55PC3UAuJCl2qpXd2Sq3FLbMv2OSc5LiNuN7BOqORNiJUnLPjjs1LkBdDyS4h4kp6i2BVV0Dz5FKklDI2uT4rkllTpbZnFxNAAAAHC2lStXKi4uTn5+foqPj9fOnTubHLtmzRpNnTpVYWFhCgsLU0JCQoPxNptNixYtUnR0tPz9/ZWQkKAjR4509GUAAOAx7O3TqRRv2oDwIEnsKw4A3/Wvfdkyl1Qo0OStu+L7y9e7Y9Jfv0wYIi+jQZ8eO6MD2cUd8hgAOgZJcQ9irnthEUX7dElSsJ+PYnvW/iy+MtNCHQAAwJ1s3LhRKSkpWrx4sfbs2aMxY8YoMTFReXl5jY5PT0/X7bffrq1btyojI0OxsbGaMWOGsrKyHGOeffZZ/elPf9Lq1au1Y8cOBQYGKjExURUVFZ11WQAAuLW8krpK8RAqxZsyMDxQknS8kKQ4ANgdNpdo18mzMki6bWKsU/YQb0p0qL+uHRUtSfrbJ8c77HEAOB9JcQ9iLq5LivNpW4fhUSGSaKEOAADgbpYvX6758+crKSlJI0aM0OrVqxUQEKC1a9c2Ov7VV1/Vfffdp7Fjx2r48OH661//KqvVqrS0NEm1VeIrVqzQY489phtuuEGjR4/WSy+9pOzsbL3zzjuNzllZWamSkpJ6BwAAaFruudr3rnrTPr1JA+xJcdqnA4AkqaKqRu/szZYkXTGolwb1Durwx/zplAGSpH9+ka2C0soOfzwAzkFS3IPYK8WjQ0mK211i31c8h0pxAAAAd2GxWLR7924lJCQ4bjMajUpISFBGRkaL5igvL1dVVZV69uwpSTp+/LjMZnO9OUNDQxUfH9/knEuWLFFoaKjjiI2NbcdVAQDg/vLrKsVpn940R1Kc9ukAIElKP5yv4vNV6hnoqx+MiOqUxxwb20Nj+oaqqsam9/bldMpjAmg/kuIeoqrG6vjEEi8svjU8mkpxAAAAd1NQUKCamhpFRkbWuz0yMlJms7lFczz66KOKiYlxJMHt57VmzoULF6q4uNhxnDp1qrWXAgCAxyi3VOtcZbUkKYJK8SbZk+InC8tVY7W5OBoAcK2z5RZt/6ZAkvTDUdEdto94Y24Y20eS9M7erIuMBNBVkBT3EPnnKmWzST5eBvUK9HV1OF3GsLpK8cO553ghAQAAAEnS0qVLtWHDBr399tvy82v7B0pNJpNCQkLqHQAAoHH2/cT9fbwUZPJ2cTRdV0wPf/l6GWWpsSq76LyrwwEAl/rwYK6qrTYNDA90vNffWX44JlpGg/R5ZpHOlFk69bEBtA1JcQ+RU7efeESwn4xGg4uj6TriegXK5G1URZVVmWfKXR0OAAAAnCA8PFxeXl7Kzc2td3tubq6ioppvp5eamqqlS5dq8+bNGj16tON2+3ltmRMAAFxc3jl7h0OTDAbeu2qKl9Gg/r0CJEnHaKEOwIOdKbPoi1NFkqSZI6M6fe2ICPbTlYPDJUl76+IA0LWRFPcQuXX7iUexn3g9XkaD4xNkX+XQQh0AAMAd+Pr6avz48UpLS3PcZrValZaWpsmTJzd53rPPPqunnnpKmzZt0oQJE+rdN2DAAEVFRdWbs6SkRDt27Gh2TgAA0DL2964ignnv6mLsLdRPkBQH4ME+PpIvm6QhEUHqGxbgkhh+ODpaEtuzAt0FSXEPYa6rFI9iP/EGhtclxQ+Zz7k4EgAAADhLSkqK1qxZo/Xr1+vQoUNasGCBysrKlJSUJEmaO3euFi5c6Bj/zDPP6PHHH9fatWsVFxcns9kss9ms0tJSSZLBYND999+vp59+Wu+++66+/PJLzZ07VzExMZo1a5YrLhEAALdirxSPCGE/8YsZ0Ls2KX6cpDgAD1VaWa3dJ89Kkq4a2ttlcXxvWIQk6fTZ8zpXUeWyOAC0DBv0eAj7p20jSYo3MDyqdm9HKsUBAADcx5w5c5Sfn69FixbJbDZr7Nix2rRpkyIjIyVJmZmZMhq//YzwqlWrZLFYdPPNN9ebZ/HixXriiSckSY888ojKysp07733qqioSFOmTNGmTZvate84AAColXeOSvGWGlhXKU77dACeas/Js6q22tSnh7/jb6IrRIT4aWSfEO3PKtGR3FJd1j/MZbEAuDgqxT2EfU/xqFA+bftdw6Pr2qdTKQ4AAOBWkpOTdfLkSVVWVmrHjh2Kj4933Jeenq5169Y5vj9x4oRsNluDw54Ql2qrxZ988kmZzWZVVFRoy5YtGjp0aCdeEQAA7iuvhErxlhoQHiRJOl5Q6uJIAM+zcuVKxcXFyc/PT/Hx8dq5c2eTYw8cOKCbbrpJcXFxMhgMWrFiRYMxTzzxhAwGQ71j+PDhHXgF3Z/NZtOuuirxSQN6dvpe4t/1/bpq8cO55BeAro6kuIcwO/YU93dxJF2PvVI880y5yiqrXRwNAAAAAACA57FXikeSFL+ouPDavXNPnz2vyuoaF0cDeI6NGzcqJSVFixcv1p49ezRmzBglJiYqLy+v0fHl5eUaOHCgli5dqqioqCbnvfTSS5WTk+M4Pvnkk466BLeQeaZcBaWV8vUyanSfUFeHo+nDa5PiR/LOqcZqc3E0AJpDUtxD2Nuns6d4Qz0DfRURXPuCi09zAQAAAAAAdL5ce6U47dMvqneQSUEmb9lsUmZhuavDATzG8uXLNX/+fCUlJWnEiBFavXq1AgICtHbt2kbHT5w4UcuWLdNtt90mk6npD/x4e3srKirKcYSHh3fUJbgFe5X4qD6hMvl4uTgaaUzfHvL38VJFlVXZReddHQ6AZpAU9wA2m03mYpLizRkebd9XnKQ4AAAAAABAZ8srse8pTqX4xRgMBg2o20P3OPuKA53CYrFo9+7dSkhIcNxmNBqVkJCgjIyMds195MgRxcTEaODAgbrzzjuVmZnZ7PjKykqVlJTUOzxFjdWmA9nFkqRx/Xu4Npg6XkaD4vibDHQLJMU9QMn5alVWWyWxL1NTLomy7yvuOf+BAAAAAAAA6AoqqmpUUlG7pV0EBR0tQlIc6FwFBQWqqalRZGRkvdsjIyNlNpvbPG98fLzWrVunTZs2adWqVTp+/LimTp2qc+eaLt5asmSJQkNDHUdsbGybH7+7OVZQqooqqwJN3orrFejqcBz4mwx0DyTFPUB+ae0nbUP8vOXXBdqJdEXDo+uS4lSKA13eypUrFRcXJz8/P8XHx2vnzp1Njj1w4IBuuukmxcXFyWAwaMWKFQ3GPPHEEzIYDPWO4cOHd+AVAAAAAAAulFfXOt3kbVSIn7eLo+keSMAA7uGaa67RLbfcotGjRysxMVHvv/++ioqK9Pe//73JcxYuXKji4mLHcerUqU6M2LUOZNcWtY2IDpHRYHBxNN+y/00+UVjGvuJAF8b/Mj1A3rnaFxa9aT/VpGGRte3TD5lLZLPZZOhCCyqAb23cuFEpKSlavXq14uPjtWLFCiUmJurw4cOKiIhoML68vFwDBw7ULbfcogceeKDJeS+99FJt2bLF8b23N8sj3NtrO5pvxfZdd8T361LzAwAAwL3knast6IgM8eM9mRayJ2COkRQHOkV4eLi8vLyUm5tb7/bc3FxFRUU57XF69OihoUOH6ujRo02OMZlMze5R7q6sNpsO1iXFL40JcXE09UWH+snPx6iKKqtyis+rb1iAq0MC0AgqxT1APknxixoUEShvo0HnKqqVU7f/OoCuZ/ny5Zo/f76SkpI0YsQIrV69WgEBAVq7dm2j4ydOnKhly5bptttua/bFgre3t6KiohxHeHh4k2M9ed8mAAAAAOgI9oIO9hNvOSrFgc7l6+ur8ePHKy0tzXGb1WpVWlqaJk+e7LTHKS0t1TfffKPo6GinzekuTp89r9LKavn5GDWwd9dpnS5JRoPB0c6dv8tA10VS3APkO15YsCdTU0zeXhrUO0gS+4oDXZXFYtHu3buVkJDguM1oNCohIUEZGRntmvvIkSOKiYnRwIEDdeeddyozs+kqV0/etwkAAAAAOkJuSW2BQkQISfGWiqtLiuefq9S5iioXRwN4hpSUFK1Zs0br16/XoUOHtGDBApWVlSkpKUmSNHfuXC1cuNAx3mKxaO/evdq7d68sFouysrK0d+/eelXgDz30kLZt26YTJ05o+/btmj17try8vHT77bd3+vV1dUfzarc+Hdw7SN7GrpfasifFM8+UuzgSAE2hP6wHoFK8ZYZHB+tw7jkdyjmn7w+PdHU4AL6joKBANTU1ioys//sZGRmpr776qs3zxsfHa926dRo2bJhycnL029/+VlOnTtX+/fsVHBzcYPzChQuVkpLi+L6kpITEOOBmaP8OAADQufIo6Gi1UH8fhQf5qqDUohMF5RrVN9TVIQFub86cOcrPz9eiRYtkNps1duxYbdq0yfFeVWZmpowXJGuzs7M1btw4x/epqalKTU3VtGnTlJ6eLkk6ffq0br/9dhUWFqp3796aMmWKPv30U/Xu3btTr607OJpXW4E9KCLIxZE0LrZnbcv0UyTFgS6LpLgHICneMsOjQvR/ytZX5nOuDgVAJ7rmmmscX48ePVrx8fHq37+//v73v+unP/1pg/Geum8TAAAAAHQUKsXbZmB4kApKz+ho/jmS4kAnSU5OVnJycqP32RPddnFxcbLZbM3Ot2HDBmeF5tYs1VZHsnlw766ZFO/Tw19Gg1RSUa3i81UK9fdxdUgAvqNNPSaOHTvm7DjQgfJL65LiQbywaM7w6NqK0K9yaJ8OOJOz1ozw8HB5eXkpNze33u25ubmKiopyymNIUo8ePTR06NB6rawAAJ2D1xkAgJZizXAvbP3XNkMiaxNDX+eWujgSoGtjzej+ThSWqcZmU1iAj3oG+ro6nEb5ehsVGVK7jlEtDnRNbUqKDx48WN/73vf0yiuvqKKiwtkxwcnySupeWPBp22YNj6pNih8rKFNldY2LowHch7PWDF9fX40fP15paWmO26xWq9LS0jR58mRnhCpJKi0t1TfffKPo6GinzQkAaBleZwAAWoo1w73Q5bBthkbWvpd1JJeuh0BzWDO6v6N5tR/+GdQ7SAaDwcXRNK1vWF0L9bMkxYGuqE1J8T179mj06NFKSUlRVFSUfvazn2nnzp3Ojg1O4qgU54VFs6JC/BTq76Maq82xyAJoP2euGSkpKVqzZo3Wr1+vQ4cOacGCBSorK1NSUpIkae7cuVq4cKFjvMVi0d69e7V3715ZLBZlZWVp79699arAH3roIW3btk0nTpzQ9u3bNXv2bHl5een2229v34UDAFqN1xkAgJZizXAvBXXvXYUHdc3qv67KnhQ/TFIcaBZrRvd3vKB2P/GBXbR1ul2/nv6SpFNnzrs4EgCNaVNSfOzYsXruueeUnZ2ttWvXKicnR1OmTNHIkSO1fPly5efnOztOtFFVjVVnyiySaJ9+MQaDwVEt/lUOLyYAZ3HmmjFnzhylpqZq0aJFGjt2rPbu3atNmzYpMjJSkpSZmamcnBzH+OzsbI0bN07jxo1TTk6OUlNTNW7cON1zzz2OMadPn9btt9+uYcOG6dZbb1WvXr306aefqnfv3s77IQAAWoTXGQCAlmLNcB81VhvvXbXR0Lr26afOnFe5pdrF0QBdF2tG91ZVY1VOcW2SuX+vABdH0zx7pXhWUblqrM3vJw+g87UpKW7n7e2tG2+8Uf/4xz/0zDPP6OjRo3rooYcUGxuruXPn1ktMwDUKS2tfVHgZDQoL4NO2F3NJdIgk6Ssz+4oDzuasNSM5OVknT55UZWWlduzYofj4eMd96enpWrduneP7uLg42Wy2Bkd6erpjzIYNG5Sdna3KykqdPn1aGzZs0KBBg5x12QCANuB1BgCgpVgzur8zZRZZbZLBoC67T2xX1SvI5KiuP8K+4sBFsWZ0T6fPnpfVJgX7eauHv4+rw2lW72CTfL2NqqqxObqgAOg62pUU37Vrl+677z5FR0dr+fLleuihh/TNN9/oww8/VHZ2tm644QZnxYk2su/JFB7kK6Ox6+610VU4KsXNVIoDzsaaAQBoKdYMAEBLsWZ0f/akQViAr7y92vVWpUeihTrQcqwZ3dOpM7X7c/frGdCl9xOXJKPBoOhQP0lSdhEt1IGuxrstJy1fvlwvvviiDh8+rGuvvVYvvfSSrr32WhmNtf9xHTBggNatW6e4uDhnxoo2yDtXIUmKCPZzcSTdw7C6pPgh2qcDTsOaAQBoKdYMAEBLsWa4D3tSnNbpbTM0MljbvynUEZLiQJNYM7q3zAuS4t1BTA9/nSwsV3bReY3rF+bqcABcoE1J8VWrVuknP/mJ7r77bkVHRzc6JiIiQn/729/aFRzaz14p3juYFxYtMSwqWAZD7Quy/HOV/Nw62Gs7Mtt1/h3x/ZwUCToSawYAoKVYMwAALcWa4T7sSfHwYFqnt8W3leK0TweawprRfdlstu6XFA/1lyRlF1e4OBIA39WmpPiHH36ofv36OT5JZWez2XTq1Cn169dPvr6+mjdvnlOCRNs5kuJ82rZFAny9NTA8UN/kl2l/VrG+NzzC1SEB3R5rBgCgpVgzAAAtxZrhPgrOWSRJ4bx31SbDooIkiUpxoBmsGd1XUXmVSiur5WUwKKaHv6vDaZGYHt+2T7fabDJ28ZbvgCdp00Y9gwYNUkFBQYPbz5w5owEDBrQ7KDhPfimV4q01qk+oJOnLrGIXRwK4B9YMAEBLsWYAAFqKNcN9OCrFSYq3yeCI2krxnOIKFZ+vcnE0QNfEmtF9ZdXtyx0ZapKPV5vSWZ0uIthP3kaDKqutOltmcXU4AC7Qpr8iNput0dtLS0vl58fe1V2JvVI8IoQXFi01kqQ44FSsGQCAlmLNAAC0FGuG+7C/d0VSvG1C/X0UHVr7nKdaHGgca0b3lVPXgtzekrw78DIaFBlSVy1OC3WgS2lV+/SUlBRJksFg0KJFixQQ8O0eDjU1NdqxY4fGjh3r1ADRPnm0T281e6X4fpLiQLuwZgAAWoo1AwDQUqwZ7iffUSnOnuJtNTQyWDnFFfo6t1QT4nq6Ohygy2DN6P5yimsrxe0f/ukuYnr4KavovHKKzjvyDQBcr1VJ8c8//1xS7ServvzyS/n6fvufVV9fX40ZM0YPPfSQcyNEuzj2FKd9eotd2idUBkPtp9AKSiv5pDLQRqwZQOd5bUemq0MA2oU1AwDQUqwZ7qegtG5Pcd67arOhkUHa9nW+vnZypXhbX2fcEd/PqXEAbcWa0f3ZK8Wju1GluCRF1VWK55ZQKQ50Ja1Kim/dulWSlJSUpOeee04hISEdEhScw2azkRRvgyCTtwaEB+pYfpm+zCrW94ZFuDokoFtizQAAtBRrBgCgpVgz3I99T3G6HLbd0MjafcUPm2mfDlyINaN7K6usVvH5KklSVDerFI+si9dMUhzoUlqVFLd78cUXnR0HOkCZpUbnq2okkRRvrVF9QnUsv0z7T5MUB9qLNQMA0FKsGQCAlmLNcA9Wq01nymorxXnvqu3sSfEjeSTFgcawZnRP9irxXoG+8vPxcnE0rRMZXJsUP1tepcrqGpm8u1f8gLtqcVL8xhtv1Lp16xQSEqIbb7yx2bFvvfVWuwND++XVfQopyOStAN82ff7BY43qE6r/25utL9lXHGgT1gwAQEuxZgAAWoo1w/2cLbeoxmqTJPUMZE/xthoSGSSpthV9YWmlelF1D7BmuIHuup+4JAWavBVs8ta5ymrllVQqtmfAxU8C0OFanCkNDQ2VwWBwfI2uj9bpbTeyT+1znKQ40DasGQCAlmLNAAC0FGuG+7HvJx4W4CMfL6OLo+m+Any91a9ngDLPlOuw+ZyuGMx7gQBrRvdnrxSP6mb7idtFhvrpXF6pcksqSIoDXUSLk+IXthih3Uj3kM+eTG12aUyIDIbahbegtFLh/AyBVmHNAAC0FGsGAKClWDPcj30/cd53ab9LY0KUeaZc+7OLdcXgcFeHA7gca0b3l3euLike0v0qxSUpMtiko3ml7CsOdCFt+gjm+fPnVV5e7vj+5MmTWrFihTZv3uy0wNB+VIq3XbCfjwaEB0qiWhxoL9YMAEBLsWYAAFrKmWvGypUrFRcXJz8/P8XHx2vnzp3Njv/HP/6h4cOHy8/PT6NGjdL777/fYMyhQ4d0/fXXKzQ0VIGBgZo4caIyMzNbHZu7s793RVK8/exdD/dnlbg4EqDr4XVG92O12hxrREQ3zW9E1bV9zyUpDnQZbUqK33DDDXrppZckSUVFRZo0aZL+8Ic/6IYbbtCqVaucGiDajqR4+4yyv5g4TVIcaA/WDABAS7FmAABayllrxsaNG5WSkqLFixdrz549GjNmjBITE5WXl9fo+O3bt+v222/XT3/6U33++eeaNWuWZs2apf379zvGfPPNN5oyZYqGDx+u9PR07du3T48//rj8/LpnpVtHclSK895Vu10aEyJJ2p/N+1jAd/E6o/vJKjqvqhqbvIwGhQX6ujqcNokMsSfFK10cCQC7NiXF9+zZo6lTp0qS3njjDUVFRenkyZN66aWX9Kc//cmpAaLt8kiKt8so9hXv8l7bkdmuA52DNQMA0FKsGQCAlnLWmrF8+XLNnz9fSUlJGjFihFavXq2AgACtXbu20fHPPfecZs6cqYcffliXXHKJnnrqKV122WX685//7Bjzm9/8Rtdee62effZZjRs3ToMGDdL111+viIiIJuOorKxUSUlJvcMT5Dvap3fPhEdXcmlM7ftYxwvKVFpZ7eJogK6F1xndz9G8Ukm164OX0eDiaNomIthPBkmlldX8XQa6iDYlxcvLyxUcHCxJ2rx5s2688UYZjUZdfvnlOnnyZKvnc3abqtLSUiUnJ6tv377y9/d3vKjxNFSKt8+3badIigPt4ew1AwDgvlgzAAAt5Yw1w2KxaPfu3UpISHDcZjQalZCQoIyMjEbPycjIqDdekhITEx3jrVar3nvvPQ0dOlSJiYmKiIhQfHy83nnnnWZjWbJkiUJDQx1HbGxsi66huys4Z5FE+3Rn6B1sUlSIn2w26VCOZ3yoAmgpXmd0P0fyzkmqTSx3V77eRkeVOy3Uga6hTUnxwYMH65133tGpU6f0wQcfaMaMGZKkvLw8hYSEtGqujmhTlZKSok2bNumVV17RoUOHdP/99ys5OVnvvvtuWy632yIp3j6XxoTIYJCyiyuUx6IFtJkz1wwAgHtjzQAAtJQz1oyCggLV1NQoMjKy3u2RkZEym82NnmM2m5sdn5eXp9LSUi1dulQzZ87U5s2bNXv2bN14443atm1bk7EsXLhQxcXFjuPUqVMtuobuzt4+vTdJcaegwANoHK8zuh97pXh3z21820Kd/ALQFbQpKb5o0SI99NBDiouLU3x8vCZPniyp9lNW48aNa9VcHdGmavv27Zo3b56mT5+uuLg43XvvvRozZsxFK9Ddjb0FVUQ3XzhcJdjPR8Miaz9BuPvkWRdHA3RfzlwzAADujTUDANBSXXXNsFqtkmr3r33ggQc0duxY/epXv9IPf/jDZrsYmkwmhYSE1Ds8gSMpzntXTjGyT+3zhq0Agfq66pqBph2pS4p399xGZEht/CTFga6hTUnxm2++WZmZmdq1a5c2bdrkuP3qq6/WH//4xxbP0xFtqiTpiiuu0LvvvqusrCzZbDZt3bpVX3/9teMTYN/ljvs2Wa02FTr2ZereC4crje8fJomkOPD/2bvv8DbLc3/g31fbsiVvS94jTjySkB0ToAkjJaG0NFDSAC0jjBbanNKm5bTpOaWl/fWkI1Bo4TQFyiplF+ih0DAMgeyQnThxpveQLC/JkrXf3x8aIWR5SH4l+fu5Ll8Q+5V8O0OP3ud+7vsejUitGURElPi4ZhAR0VBFYs3IysqCXC6HyWQ65fMmkwlGo/GMjzEajee8PisrCwqFAtXV1adcU1VVhebm5iHFNZ5YuHcVURcUBCrF97T0SRsIUYzhfUZ8EUUxXCkez+3TAcAYrhR3SRwJEQGAYqQPNBqNp90gzJ07d1jPca42VfX19Wd8zPnaVAHAn/70J3zrW99CQUEBFAoFZDIZnnjiCcyfP/+Mz7l69Wo88MADw4o91vUNeuAXA/+fEZxbQcM3uyQdf9/WjJ3NTIoTjUYk1gyizzpismFPSx9aex3QKOXIS03CFyZmIZObaURxj2tG/BNFEXta+nC8awAen4hLyrNQmKGVOiwiSkCjXTNUKhVmzZqF2tpaLFmyBECg0ru2thYrVqw442PmzZuH2tpafP/73w9/7v333w9XHapUKsyZMweHDx8+5XFHjhxBcXHxkGMbD/x+EZaB4ExxHfeuImF6YaC440SXHX0ON9K0/H0lCuF9Rvzosrlgc3ohAMhKie/Xsc+2TxdFEYIgSBwR0fg2oqS43W7Hb37zG9TW1sJsNodbQ4WcOHEiIsGN1J/+9Cds3boV//d//4fi4mJ88skn+O53v4u8vLzTqsyBwNymlStXhn9ttVpRWFg4liFHXI89cPIoNUkJpXxEDQEIwKyiDACBWUxOjw8apVziiIjiT6yvGRRfHC4v/rGrFYc6bad8vrV3EDuaenBFlQGXTsqWKDoiGi2uGYnhw8Nm1B4yh399oK0fl1fm4IoqwzkeRUQ0PJFaM1auXIlbb70Vs2fPxty5c/Hwww/Dbrdj+fLlAIBbbrkF+fn5WL16NQDg3nvvxYIFC/Dggw/i6quvxksvvYQdO3bg8ccfDz/nfffdh2XLlmH+/Pm47LLLsG7dOrz11ltYv359ZH74BNE36IEvWNGRmczDrZGQkaxCaVYyGix27G7pw2UVOVKHRBQTeJ8RX05Y7ACA9GQVFHGe28hKUUMuCHB5/egb9CCdh5WIJDWipPidd96Jjz/+GDfffDNyc3NHfLolGm2qBgcH8dOf/hRvvPEGrr76agDABRdcgD179mDNmjVnTIqr1Wqo1Yn15jt00jaTVeJn9cK287ctE0URKWoFBlxePPTeEZRkJYe/dlNNUTTDi7qh/PznE++/BzQ2IrVmEPU53Hh6UyO6BlyQCUBNaSYqjDq4vX7saOrBEdMA3j9oQp/DjRvnFkEm4981onjDNSP+7W3pCyfE503IxKDbhz0tfaitNyM9WYWZRekSR0hEiSJSa8ayZcvQ1dWF+++/H52dnZg+fTrWrVsX7lLY3NwMmezkhvxFF12EF154Af/93/+Nn/70p5g4cSLefPNNTJkyJXzNtddei7Vr12L16tX43ve+h4qKCvzjH//AJZdcMrofOsGEWqenJimhUsR30iOWzChKCyTFm3qZFCcK4n1GfGnqDiTFEyG3IZcJyNap0Wl1wtTvZFKcSGIjSor/+9//xttvv42LL754VN88Gm2qPB4PPB7PKTcsACCXy087AZbIeuzBpHictxeRmiAIKM7Uoq7diuYexylJcSIamkitGTS+Dbi8eHJjA3rsbqQmKXHLvGLkpiaFvz4lPxVbT3Tjrb3t+LSxF4/UHsUPvjhJwoiJaCS4ZsQ3r8+Pdw8GxlrNn5iFxVNyAQSq1j6sN+PN3W3ITdWc8vpNRDRSkVwzVqxYcdZ9qDNVdy9duhRLly4953PefvvtuP3220cdWyKz2ELzxLl3FUkzi9Lx+q427GrukzoUopjB+4z40tjtAJA4uY0cfTApbnWiMlcvdThE49qIjmGmp6cjIyMjIgGsXLkSTzzxBJ599lkcOnQI99xzz2ltqlatWhW+/t5778W6devw4IMPor6+Hr/4xS+wY8eO8M2LXq/HggULcN9992H9+vVoaGjAM888g+eeew7XXnttRGKOB93B07acJz56RcH5h6ETakQ0PJFcM2h8cnl9eH5rE3rsbqRrlfj2/LIzJlQuLMvEdTPzAQCP1B7FugMdYx0qEY0S14z4tq6uE30OD7Qq+Smt0i+vzEGFQQevX8Q/97TDL4oSRklEiYJrRvzrGgglxROre6PUZhSlAQD2tPSF29MTjXdcM+JLoyVUKZ4Y60Norrg5eBiMiKQzoqT4r371K9x///1wOByjDmDZsmVYs2YN7r//fkyfPh179uw5rU1VR8fJTe1Qm6rHH38c06ZNw2uvvXZam6qXXnoJc+bMwTe+8Q1UV1fjN7/5DX7961/j7rvvHnW88aI7XCmeGAuHlIozA9XhTT0OiNzAIxq2SK4ZND6tefcwmnsc0ChluHVeCdLO0WpqVnEGLp6QCQBY9fp+dPGGgyiucM2IX6Io4okNDQACh5SUn5n9JxMELJmRD5VChuYeB3Y19UoVJhElEK4Z8S80+i9Lx72rSKow6KBVyTHg8uKIySZ1OEQxgWtGfEm4SvHgOsekOJH0RtQ+/cEHH8Tx48dhMBhQUlICpVJ5ytd37do1rOeLdJsqo9GIp59+elgxJJpuzhSPmLxUDRQyAQ63D90Dbt6sEQ1TpNcMGl82HbOEkyxLZxUiJ3i69lwWTTGix+HBoQ4r/vvN/fjLzbOjHSYRRQjXjPh12GTD3pY+yGUCLizLPO3rqUlKLKzMwTsHOvHuQROm5qdCrZRLECkRJQquGfEvNFM8mwUdEaWQyzCrOB0bjlqw9UQ3qtiql4hrRhwRRfEzM8UTY33I0QX2srpsLvhFETLOtCeSzIiS4qH53xS7wjPFmRQfNYVchvz0JDR1O9DU42BSnGiYuGbQSPU7PPjhK3sBAHNLMoa8maOQyfDg0mm45tGNeLfOhA/rTbi80nD+B1LceGFb87Cuv6mmKEqRUKRxzYhftYfMAIDy7BSkqM98mzlvQha2NfSg2+7GhmMWLKziazMRjRzXjPgXmimezX2WiLtoQhY2HLVg8/FuLL+4VOpwiCTHNSN+dNlccLh9kAlAerLy/A+IAxnJKsgFAW6fH/2DHqSfowMiEUXXiJLiP//5zyMdB0VY6LRtBk/bRkRxhhZN3Q4099gxqzhd6nCI4grXDBoJURTxX2/uR6fVidKsZHxpau6wHl+dp8cdl5TiL5+cwK/+dQgXl2dBrWBFIlGs45oRvz6qDyTFK4y6s14jlwlYNNmIF7Y3Y8PRLswtzYBekxgbXUQ09rhmxL+TM8WZHIi0ecGRUltPdMPnFyGXsSqRxjeuGfGjIThPPD89CQrZiKb/xhy5TECWTgWT1QWz1cWkOJGERvyq0tfXhyeffBKrVq1CT08PgECbkba2togFRyMXqhTPYqV4RBRlBOeKd3PuDNFIcM2g4frnnnb8a18H5DIBf1g2HSrF8N+yrLi8HNk6NRosdjy3uSkKURJRNHDNiD+9djd2NQfmhFeeIykOAJPz9CjK0MLjE8PV5UREI8U1I75ZwklxFnRE2pQ8PXRqBWxOLw62W6UOhygmcM2ID6H995LMZIkjiaxQC3WzzSlxJETj24iS4vv27cOkSZPw29/+FmvWrEFfXx8A4PXXX8eqVasiGR+NUHcwKZ7B07YRUZypBQCYbS4MuLwSR0MUX7hm0HC19jrws38eAADce8VETC9MG9Hz6DRK3HdlBQDgf9cfg83piVSIRBQlXDPi08dHuuAXAwnxtPNUPQiCgKumGAEAOxp7YLZyU4iIRoZrRvyz2IIFHUyKR5xCLkNNWQYAYPNxi8TREEmPa0b8aAzOE0+0pHhoVIg5ODqEiKQxoqT4ypUrcdttt+Ho0aPQaDThz3/pS1/CJ598ErHgaGR8fhG9jmBSnJXiEZGsVsCoD/xdP9E1IHE0RPGFawYNh88v4oev7IXN6cXMojR859IJo3q+62bmoyw7Gb0OD57a2BiZIIkoarhmxKcNRwOb7ZdW5Azp+uLMZFTn6iECeOdAB0RRjGJ0RJSouGbEN1EU0W0PVopzpnhUzJuQBQD45GiXxJEQSY9rRvxo7glUihdlaCWOJLJyQklxHgomktSIkuKffvopvv3tb5/2+fz8fHR2do46KBqdXocboX2lDM6niJgJ2YHTaSe67BJHQhRfuGbQcDyx4QS2NfQgWSXHH5ZNh0I+uvlRCrkMP1g4CQDw5IYT6AseGiOi2MQ1Iz7tbgm0Tq8pzRjyYxZPNkIuCDhiGsChDlu0QiOiBMY1I771D3rg8QU2rzJZ0BEVl1cGDqttO9EDK7tm0TjHNSN+tPQOAgAKM5IkjiSycvSh9ukuHgomkpBiJA9Sq9WwWk+fR3PkyBFkZ2ePOigandA88TStctTJBDqpLDsFm4534zgrxYmGhWsGDdXelj48+N5hAMDPvzIZxRFqlXX11Fz87/rjONRhxdqPT+AnV1VG5HmJKPK4ZsSfPoc7fGh0emEaOvqHtqmYpVPjkolZ+PhIF/61vx0TcpKhVsiH9b19fhGDHh8EBDo7RZPX78fmY9042GGFzelBhVGPS8qz2JmLSEJcM+JbaJ64TqOARjm8138amtKsZJTnpOCYeQAfH+7CV6blSR1SzHhhW/OIHndTTVGEI6GxwjUjfrT1BirFC9K16LH3SxxN5GQlqyATAJfXD6vTi9QkpdQhJTy+1tOZjChjes011+CXv/wlPJ7AKUNBENDc3Iwf//jH+NrXvhbRAGn4ugcCSXGetI2s0qxkCAjMa2elIdHQcc2goehzuPGdv++CxyfiqilGLJ1dELHnlskE/PCLgWrxZzY3sFUVUQzjmhF/9rT0AQi8V04f5v3HZRU5SEtSos/hwVt7O4b8uBNdA3h+axN++a86/M87h/Drdw7hD+8fwceHzXB5fcOKYShsTg/+uqEB6+o60dzjQK/Dg60nurH24+PhA8lENPa4ZsS3ruA88WzOE4+qhVUGAMAHh0wSR0IkLa4Z8cHh9sISzG0UpidW+3SFXIaM5NBcce5LEUllRMfpH3zwQVx//fXIzs7G4OAgFixYgM7OTsybNw+//vWvIx0jDVNoJlNmMm8sIkmjlKMgPQktvYM43mUf8UmjkEQ4cTTa3wMaH7hm0Pn4/SJWvrIXbX2DKM7U4rfXXwBBECL6Pa6oysH0wjTsaenD2o9P4P6vVEf0+YkoMrhmxJ/dzX0AgBmFacN+rEohw/WzC/DXDQ3Y1dyL0qxkzCpOP+v1lgEX1h3oxMGO06t8ugZcePegCYv+8An+/M1ZqMrVDzueM3F6fHhqUwNMVhfUChmunGxEWpIS7x3shMnqwlObGvDdS8uRpGKVI9FY45oR30KV4pwnHl1frM7B2o+P46N6Mzw+P5TsKEnjFNeM+NAWbJ2u0yiQqk28SuocnRqWARfMVhcm5uikDodoXBpRUjw1NRXvv/8+Nm3ahL1792JgYAAzZ87EwoULIx0fjUCoWiEzhZXikTYhJwUtvYM4YrKdc8OOiE7imkHns/aT4/iw3gy1Qob//cZM6DWRv/ERBAE/+OIk3PrUdrywvQnfvWwCMlmVQhRzuGbEn93BSvHpRWkjenxZVgouq8zBh/VmvL6rFXIZML3w1PfZg24fPqw3YcuJbvhFQCYAs0syMLckA8ZUDVweP+ra+/H+IRMaux34+tot+MvNs3BRedaofja/KOKlT5thsrqgUytw5xfKkB1M3uSnJ+EvwUrxj490YfEU46i+FxENH9eM+NZlCyTFWSkeXdML05GVEkjCfHKkC1cEK8djEdvcUjRxzYgPLZ9pnZ6IcvRqHOwIzBUnImkMOynu9/vxzDPP4PXXX0djYyMEQUBpaSmMRiNEUYx4ZRcNX6jFCOfbRV6FQYf1h7twzDwAvyhCxr/vROfENYPOZ92BTqx5NzBH/JdfnYzJealR+17zJ2bhgoJU7Gvtx5MbG/DjxZwtThRLuGbEH79fxJ7mXgDAjMKRHxi9vDIH1kEPdjT14pUdrTjYbsUFBWmQCcBxix27mnrh8voBAJMMKbhqSi4Mek348UkqOWaXZKA6T4/3DpqwvaEHdzy7A8/fWTOqg6zrD3fhiGkASrmAm+cVhxPiAKDXKPGVC/Lw3NYmbD5uwbwJmZwLSDSGuGbEv3ClOAs6okouE7Bkeh6e3NiA13a2xnRSnChauGbEj9ZgpXhhepLEkURHji5wD8P26UTSGVbPHFEUcc011+DOO+9EW1sbpk6dismTJ6OpqQm33XYbrr322mjFScPQE2qfztO2EVeQroVGKcOgx4fWHofU4RDFNK4ZdD6bj1nwvZd2wy8GTvd/fXZhVL+fIAhYcVk5AOC5zY3oc3AOLFGs4JoRn1p7B2F1eqGSy1CZO/L2fzJBwJIZ+fjCxCwIAA60W/HC9mY8v60ZW453w+X1w6BX47aLSnDbRaWnJMQ/S6tS4G93zMWCSdkY9Phw+zOf4ph5YEQxNXXb8WF9YP7qkun5Z6xWqTDqUJyhhdcvYv1h84i+DxENH9eMxHAyKc69q2j72qwCAIG54r123gPR+MI1I7609CR4pXjwkK3Z6oIoihJHQzQ+DatS/JlnnsEnn3yC2tpaXHbZZad87cMPP8SSJUvw3HPP4ZZbbolokDQ83cFK8UxWikecXCZgYo4O+9v6cdg0gKLMZKlDIopZXDPoXD6sN+Ge53fB7fXji9UG/PKayWNyOnthlQGVRh3qO214ZnMjvr9wUtS/JxGdH9eM+FTfGZjtPSEnZdQzSmWCgKum5GJGYTo2H7eg0+qEXxRh1GtwQUEaynNShtSlSa2Q48/fnIlvPLkNu5v7sPyZ7XjzOxcP68DwoNuHlz9tgV8EphemYUbRmavNBUHAwmoD/rqxAXta+nDVlFyoFJzVShRtXDMSQ6jLIWeKR19Vrh6T8/Soa7fin3vacNvFpVKHNK6wLby0uGbEl3CleEZiVopn69QQAAx6fBhweaGLwvhAIjq3Yd2xv/jii/jpT3962gICAJdffjl+8pOf4O9//3vEgqOR6eZM8aiaZEgBABwx2SSOhCi2cc2gMxFFERuOduHOZ3fA5fVjYVUO/nTjDChGmUwZKplMwIrLA9XiT29qhM3pGZPvS0TnxjUjPoXeD1caR14l/nnGVA2um1mA71xajhWXTcT1swoxyaAb1tgirUqBJ2+ZjaIMLVp6BnHXczvg9PiG9FhRFPGPXa3oG/QgI1mFa6blnfP60qxkZCSr4PIG5poTUfRxzUgMrBQfW6GuXE9vboTX55c4GqKxwzUjviT6THGlXIb0YCEj54oTSWNYO9D79u3D4sWLz/r1q666Cnv37h11UDQ63cEbC84Uj45JBh0EAG19g+gfZDKF6Gy4ZtDndQ+48PTmRvz7QCf8InD9rAL8+ZuzoFHKxzSOq6bkoiw7Gf2DHjy/dWSn9okosrhmxKf6zkBSfJIhcknxSMlMUeOp2+ZAr1FgV3MffvTqXvj9529R+PzWJhzssEIuCLhxTtF51yiZIGBGURoAYFdwvjoRRRfXjMRgsXGm+FhaOrsAGckqNHU78Pb+DqnDIRozXDPiS0tPYleKA59poc6kOJEkhpUU7+npgcFgOOvXDQYDenu5ESC1nmClOE/bRodOo0RhRuC02qEOq8TREMUurhkEAH5RRIPFjld3tOAPHxzBMfMAFDIBv/zqZPz++gtG3W53JOQyAd+9NFAt/uSGExh0D616kIiih2tGfIpGpXgkleekYO3Ns6CQCfjXvg489P6Rc15/oK0fv3r7EABg8RQj8tOHthk3M9he/USXHX0OzmolijauGfFPFMWT7dO5dzUmtCoFbr+4BADw2EfH4BvCQTGiRMA1I37YnJ5wAVqiVooDQI5OAwDosjkljoRofBrWTrTP54NCcfYx5HK5HF6vd9RB0ch5fX70OgKLByvFo6c6Vw8AONjOpDjR2XDNGD88Pj8sNheOdw1gV3MvPjpsxpu72/D0pgb8+u1DeGLDCexu6YNfDIyguPeKibhlXsmYzBA/m2um56EwIwnddjde3M5qcSKpcc2IP26vHye67ACAihhNigPARROysPq6qQCARz86hue2NJ7xugaLHbc9/SncXj8qjTpcNCFzyN8jXatCcaYWIk5WzxNR9HDNiH9WpxfuYAvvbM4UHzM3zyuBTqPAEdMAnt3cKHU4RGOCa0b86OwPJIn1GgVS1Gf/M4t3OfpgpbiVleJEUhjWq4soirjtttugVp/5DavLxX/IUgslxAUhsDlD0TE5T491dZ04YRnAoNuHJNXYtv4ligdcMxKbzy9ib2sfdjX1oqnbAZ949koDtUKGyXmpqCnNCHfakJpSLsM9C8rx0zf24y+fHMd3Ly0fs7nmRHS6aK0Zjz32GH7/+9+js7MT06ZNw5/+9CfMnTv3jNfW1dXh/vvvx86dO9HU1IQ//OEP+P73v3/KNb/4xS/wwAMPnPK5iooK1NfXjyi+eHbCMgCvX4ROo0BuqkbqcM5p6exCNHU78OhHx3D/P+vQaHHgh1dOQnJws23TMQtWvrIHlgEXKo06LJ1VOOyDW5VGPZq6HTjMpDhR1PE+I/51BVvG6tSKMR+lNJ6lJinxk6sq8V9vHMCa9w7ji9WGYd+fiaIIu9uHLpsLTk+g41aKWoEcnRpq/llSDOKaET/ag0nx3NTEbZ0OsH06kdSGlRS/9dZbz3vNLbfcMuJgaPS67YEX03StCnKZdBV4iS4zRQ2DXg2T1YVDHVbMLE6XOiSimMM1I3GZbU68sK35lDfwKrkMqUnK8Ic+SYm0JCVy0zTITU2KyTXpa7Py8cfao+i0OrGzuRc1pUOvCiSiyIrGmvHyyy9j5cqVWLt2LWpqavDwww9j0aJFOHz4MHJyck673uFwoKysDEuXLsUPfvCDsz7v5MmT8cEHH4R/fa7Kk0QWSv5WGHSSdv4Yqh9eOQlKuQx/+OAIntrUgH/uacPsknSYbS7sbu4DAEzMScHzd9bgvTrTsJ+/wqjDu3WdON7FQ7NE0cb7jPhnGQjOE2eV+Ji7cU4R/rm7Hdsbe3DnszvwyrfnIVWrPOdjQiOx9rf246jZFi7I+SwBQF5aEibn6TGLe2QUQ7hmxI/O/sA88dy02D5wO1qhDikDLi8cLi+0CVwVTxSLhvUv7umnn45WHBQhPcGZTGydHn1T8lNhspqxt7WPSXGiM+CakZiauu14dksjnB4/kpRyfGFiFqbkpyIzWRUXSZHPUivk+PaCMjzw1kF8cqQLs4szYjJ5TzQeRGPNeOihh3DXXXdh+fLlAIC1a9fi7bffxlNPPYWf/OQnp10/Z84czJkzBwDO+PUQhUIBo9EY8XjjTSgpPimGW6d/liAIuHfhRFTl6vA/7xxCY7cD734m+X1TTRH+++oqaFUj25Qy6NRIS1Kib9CDzcctuKLq7LMriWh0eJ8R/8JJ8RTuXY01mUzAg1+fhq/9eTMOm2z45l+34Y83zkBpVvIp14miiL2t/fj3gQ7sa+0Pz/kFAgnwNK0SyWoFRDEwB9jq9KKtbxBtfYP4sN6M5h4Hvj1/AooyY6NTGI1fXDPiR3tfqFI8sZPiaoUcaVol+hwemG0ulDApTjSm+C8uwVjsgaR4JpPiUTe9IA21h8w43jUAm9MDnebcJ2uJiOJd/6AHf9vaBKfHj6IMLb55YXHcz3m6YU4RHvvoGCwDbuxp6WNVA1GCcLvd2LlzJ1atWhX+nEwmw8KFC7Fly5ZRPffRo0eRl5cHjUaDefPmYfXq1SgqKjrjtS6X65SWjFardVTfO5Y0WALzxCdkp0gcyfBcOdmIyypzsOmYBY0WO7QqBeZPyoZxlJtvgiCgwqjDtoYefFhvZlKciOgcLLZQUpyV4lIozNDiuTvmYtlftmJ/Wz8WP/wJFk8x4oKCNAgAjncN4KN6c7iVMQBolDJMzU9FdW4qSrK0UCtO7YhidXpwuNOGHY09aOkdxN+3NeOlT1vwjZoi/PCLFeetRici6hwn7dOBQAv1cFL8c4eSiCi64nsnm07TEzxtm8nTtlGXmaJGQXoSWnsHcaCtH/MmZEkdEhFR1Pj8Il7c3gyH24e8VA3uuKQUygSYwZ2kkuOuL5Rh9b/rsf6wGTOK0iCLs4p3IjqdxWKBz+eDwXBqYtJgMIxq/ndNTQ2eeeYZVFRUoKOjAw888AC+8IUv4MCBA9DpTq+YXr169WkzyBNFKCleFoebOEq5DJdW5AAVkX3eSYZAUnzL8e7IPjERUYKxBLscMikunUqjHm9/7xL85B/7sfGYBf/c045/7mk/5RqtSo4J2SmYmp+KCqPunPd/eo0Sc0oyMLs4HQ3ddhzutGHDUQue29KEf+3rwH8uqsDXZxdCxs5cRHQW7cH26aM9rBoPcnQaHDENwGxznv9iIoooJsUTTHe4Upw3FmNhWkEaWnsHsaelj0nxKHC4vTjcaUNjtx2d/U7Y3T6IogitSgGjXoOy7GRU5eqhUXJmI1G07W7uRXOPAxqlDDfVFCdEQjzkGxcW4+EPjqLb7sb+tn5MK0iTOiQiilFXXXVV+P8vuOAC1NTUoLi4GK+88gruuOOO065ftWoVVq5cGf611WpFYWHhmMQaTX6/iMbuQFKclQ0nlWQmQwBwwmKH2epEjj7xN/SIiEbiZPt07l1JqSBdi7/dMRe7mvvw3sFOtPc54fX5UZSpxayidMyflI3Xd7UN6zkFQUBZVgr+++pqbD5mwc//rw5HzQP4yev78cL2ZvzimsmYWcTuXER0ulCleN44qRQHALPNdZ4riSjSmBRPMKGkOGeKj40LClLx7wMdaOkdhMnqhIEbXxHR0uPAxmMW1LX3wy+e/vVehwdtfYPY2dwLpVzAnJIMLJiUzRb2RFHi8flRW28GAFxeaUi4NSZFrcDF5Zn44JAZ6w+bMTU/ldXiRHEuKysLcrkcJpPplM+bTKaIzgNPS0vDpEmTcOzYsTN+Xa1WQ61OvA1/k80Jp8cPhUxAQXrib1oNVZJKDmOqBh39Tmxr6MFXpuVJHRIRUUwKJ8V1iXVfMVZe2NY8osfdVHP6uBdBEDCrOD0qY6QuKs/CO/d+Ac9tacLD7x/BvtZ+XPe/m3HdjHz85+LKcVENSkRD1xFMio+H14bQ4VmzlZXiRGMtccq8CADQHT5tyxuLsaDTKFFp1AMAdjT2SBxN/LPYXHhmcwP+/PFx7G8LJMQNejXmT8zCDXMK8e35Zbh7fhm+UVOEBZOykZ2ihscnYvPxbvzhgyPY2dQLUTxDFp2IRmVbQw/6Bz1ITVKipjRD6nCiYl5ZFtQKGUxWF+o7EmfmL9F4pVKpMGvWLNTW1oY/5/f7UVtbi3nz5kXs+wwMDOD48ePIzc2N2HPGg4auQJV4YYY2oTqHREJpsHJ+6wm2UCciOpsutk8fN5RyGe64pBS1P1qA62cVAABe392Gy9asx0PvH0G/wyNxhEQUC2xODwZcXgBA7nhIigcrxa1OL5wen8TREI0vrBRPMD3hSnHeWIyVOSXpONhhxa7mPlw52ciNwRHw+UVsPNqF2nozvH4RMgGYXpiOiyZkIi/tzNVHk/NScWW1Ace6BvBenQltfYP4x65WNFjsWDI9Dwr+ORBFhF8Uwxv7l1fkJOxrXJJKjnllmVh/pAsfHe5CVa4eAqvFieLaypUrceutt2L27NmYO3cuHn74YdjtdixfvhwAcMsttyA/Px+rV68GALjdbhw8eDD8/21tbdizZw9SUlJQXl4OAPjRj36Er3zlKyguLkZ7ezt+/vOfQy6X48Ybb5Tmh5RIQ7B1eilbp5+mLCsZm493Y1sDD8wSEZ2Nxcb26eNNjk6DNUun4eYLi/HLfx3EzqZe/LH2KJ7a2IBb5hVj+cWlUodIRBIKVYnrNQokqxM/ZaVRyqHXKGB1etlCnWiMJf4rzDjTHTxtm8lK8TEz0aBDapIS/YMeHGjrxwzORhqW9mAyO/TmZ2JOCq6ZlofMIdwcC4KAiTk6lGWlYMPRLrx/0IRdzb3odbhx67wSqBSJmbwjGksnuuzosbuhVsgwrTBN6nCi6qLyLGw6bkFb3yCOmAZQYdRJHRIRjcKyZcvQ1dWF+++/H52dnZg+fTrWrVsHg8EAAGhuboZMdvK9Qnt7O2bMmBH+9Zo1a7BmzRosWLAA69evBwC0trbixhtvRHd3N7Kzs3HJJZdg69atyM7OHtOfTWqNluA88UwmxT8v9HtyzDwAy4CLCR8ios8RRRFdwS6HoUo5SjznavF+3Yx8TDLo8FG9GZ1WJ/53/XGs/fg4Kgw6zCpOxySjDgoZ93OIxpPQvvDZiqMSUY5eA6tzgC3UicYYk+IJJjRTPDPB5r3GMpkgYG5pBt4/aMKm4xZML0xjdeEQeHx+fFhvxoajXfCLQJJSji9fkDui3z+5TMClFTnIT0/CC9ua0WCx45nNjbjtIibGiUbr0+BoiOmFaQn/7ylFrUBNaSY2HrPgo8NmTDKk8PWcKM6tWLECK1asOOPXQonukJKSkvOOYXnppZciFVpcawgmxUuzmRT/PK1agYk5KThqHsDu5j58sdogdUhERDHF5vLC7fUDYKX4eCUIAqbmp2Jynh71HTZ8fMSMlt5BHOq04VCnDSq5DBOykzHRoENJVjJydGrIznFfNtIZ60QUOzr7BwGMj3niITk6NY6ZB1gpTjTGmBRPIB6fH/2DgVk8Q6mypciZU5KBj+rNaO9zoqnbgRK2kjynBosdb+xuhSXY2WBqfiq+fEEudBrlqJ53Yo4Ot19ciqc2NaCx245Xd7bgxrlF57x5IqKzG3T7cDA4X3t2SWLOEv+8S8qzsPVEN5p7HGiw2FGWnSJ1SEREMSecFGel+BnNLEoPJsV7mRQnIvqcUOv0ZJUcSSq5xNGQlGSCgOo8Parz9DBZndjV1Is9rX2wOb3hBDkAqOQy5KUloTA9CXlpScjRq5GdoubYPKIE0t4XqJbOTR1HleK6wAEAs42V4kRjiUnxBNIbrBKXCUBa0uiSizQ8KWoFphemYUdTLzYeszApfhZOjw/v1nWGZyzqNAp8dVoeqvNSI/Y9CjO0uHVeCf66qQF17VZ8cMiEK6uNEXt+ovHksMkGn19Ejk6N/HHSwkqfpMSs4nRsa+jBR4fNTIoTEX2Ozy+iuccBACjJ0kocTWyaUZSGl3e0YHdzn9ShEBHFnNDh+Cy2Th9zsVxRbdBrcNXUXCyaYkRnvxNHTDYcNQ+grW8Qbq8fjd12NHbbw9fLBCAjWQ2DXo0cnQbZOhXStSpkJKuQolaw4xdRnOnsDyXFx1elOIC4qxQf6VpyU01RhCMhGhkmxRNIqHV6RrIKMhnf/I21i8uzsKOpFwc7rOi0OmHUj59F/HxEUURduxVv7+8IdzOYU5KOxZNzo3IyvCQrGdfNyMerO1ux/nAXSjMDbbeIaHjqOwNV4lW5eokjGVvzJ2Xj08YeHO+yo7nbjiJWQhIRhXVanfD4RCjlwriq5BiOGUXpAIC9rX3w+UXIeW9GRBRmCc4TZ+t0OhOZICAvLVARfmlFDvyiiC6bC629g2jtdaCj3wmzzQmnxw/LgAuWARfqYD3lOZRyAWlJKiSrFdCq5EhWy6FVKZCklEOjlEOjlEGtCPxXE/ycViWHkpXnRJJpD7ZPH49J8T6HB3aXF8lqpuqIxgL/pSWQ7oGTSXEaewa9BpPz9Khrt2L9YTNumMPTT0Dghvetve04ah4AEPj7ee2MfEyIcvXljKJ0NPU4sL2hB6/sbMW9V0xECt9cEA2Z1+/HEVOgXV2VcXwdKknXqjCjKB07m3rx0eEu3HoRk+JERCEtwSrx/LQkJnvPojwnBSlqBQZcXhwx2cbd4TIionM5mRTn3hWdn0wQYNBrYNBrMKs4cOhMFEVYnV6YrU6YbC6YrU50293otbvRP+iBxyeia8CFroGhV18KANKTVchOUSM3VYNyQwqKMrRQyJgoJxoLJyvFx8+hW61agWS1AnaXF8e7BnBBQZrUIcWsWO50QvGHGaIE0m0PvNnLTOZpW6lcVpGDunYr9rf24/IKJ3LGcbV4/6AHHx02Y2djL3xioEJm/sRsLJiUDZVibG4qrp6ai6ZuO0xWF97e145lPKhANGSNFgecHj+S1QoUZIy/9rgLJmVjV1MvDptsaO8bRN44aR9PRHQ+oaR44ThcG4ZKLhMwrTAVm451Y3dzH5PiRESfEZopzkpxGilBEJCapERqkvK0roBevx/9Dg/6BgOVlw63Dw63F3aXD06PD06vH06PD67P/L/T44NfBHrsbvTY3ThssmH9kS6oFDJMMuhQU5qBsqxktmQniqKOUFI8bXztpefo1GhweXHUxKQ40VhhUjyBhCvFedpWMnlpSajO1eNghxXvHTThmxcWSx3SmOuyubDlRDd2NPbA6xcBAJMMKfjyBXljftOrlMvwtZkF+PP649jb2o/phTZUjLOKV6KROhxsnV5p0EE2Dm/+s1LUuKAgFXtb+/HRYTO+UTP+Xs+JiM6kpTfQ2rAgnUnxc5lRmB5Mivdyfh4R0Wd0sX06RZFCJkNmihqZw/j7JYoi7G4fzDYnumwuNHU7cNRkg93tw4G2fhxo60dWihrzJmTia7PyoVZEfgwg0Xhmc3ow4PICGF/t04FgUtxixz/3tMPl9Q/78bzPIBo+JsUTSE9wpngm26dL6spqAw51WHGww4qmbjuKx8EsWrfXj8MmGz5t6MGxroHw50sytfhitRGlWdL9HhSka3HRhExsOt6Nt/a1Y0L2RCg4J4rovBq67QCAckN0Rx3EsksrcrC3tR917VaYrE4YxnH3DyKikNZgpXhBOjtonMvUglQAwP62fokjISKKLV22wN5Vto5JcYoNgiAgRa1AijoFZVkpqCnNhF8U0d43iJ1Nvdjd0hceDbi7uRc/vHISvjotHzKOkSGKiFCVeGqSElrV+EpXhbrMmm1OiSMhGj+YGUogbJ8eG3L0GswuCcw5+te+DvhFUeKIosPl9WFfax9e2NaEX79zEC9ub8axrgEIACqNOtx+cSnu+kKZpAnxkIXVBujUCvTY3dhyolvqcIhintXpQUdf4A15yTg42HM2Br0Gk/MCLW8/PtIlcTRERLGhNVgpzvbp5zYlP5AUP2oegNPjkzgaIqLYYWGlOMUBmSCgIF2Lr07Px6rFlfjKBbnQaRRo7R3ED17ei688uhF7W/qkDpMoIYRbp4+zKnEgUCkOAObgaBEiir7xdfQmwbF9euxYWGXA/rZ+tPUNYtuJbsybkCV1SBEx6PahvtOKA+1WHDXZwu3RASBdq8QFBWmYW5KB9BjrVqBWyPHFagNe392Gjw6bMasoHVo1X/6IzmZnYy9EABnJKqQmKaUOR1KXVuSgrt2KvS19uKIyZ1ht+IiIElFLb3CmOCvFzykvVYOMZBV67G7Ud9owvTBN6pCIiGJCKCmerYutfQOis1Er5Zg3IQuzijPg8Hjx5/XHUdduxZL/3YRb55Xgh1dOgk4zvu+biUajoy9w6HY8J8V77W54fH4o2d2UKOr4ryyBdAfbp2fFWEJyPNJplFg02QgAeO+gCf2DHokjGjmv3489Lb14ZnMD/uedQ3h1ZysOdVjh9YvITFZhwaRsfPeycvzoygosmmyMuYR4yMzidOSmauD0+LHhmEXqcIhi2raGHgBA6TiuEg/JT0tChUEHEawWJyJyeX3otAYqOVgpfm6CIISrxdlCnYgoQBRFVopT3FIpZPjOpeX46EeX4toZ+RBF4JnNjVj0h0+wmftMRCMWqhQ3po6/Q7cpagWSlHKIOHlojIiii0nxBBKaKZ4Ro0nJ8WZOSQYK05Pg8vrxr33tUoczbAMuLz44ZMLv1h3GKztaccQ0AJ8oIkenxmUVOfiPy8ux8ouTsGiyEflpSRCE2J6lJBMEXFFpAABsOdENu8srcUREsWt7Q2DMQCyMP4gFl1VkAwB2Nfeiz+GWOBoiIum09zkhikCSUo5M3nOc15TgCI46JsWJiAAE9hmcHj8AJsUpfmWlqPGHZdPx/B01KMrQor3fiZue3IYH3qrjyBSiEejoD1SK543DSnFBEJCjD7ZQtzIpTjQW2D84gYROE7G1a2yQCQKunVGARz86irp2Kw51WFGVq5c6rPPy+vzYfLwbHx02w+UN3KzqNQrMKcnA1IJU5Oji9w1KVa4OeWkatPc5sfGYBXfNL5M6JKKY4/T4sK81sHlfwqQ4AKAoMxll2ck40WXHJ0e7cM20fKlDIiKSREtPsHV6RuwfiIwFU0dYKf7CtuZRfd+baopG9XgiomixBMf+aVVyJHOkGcW5SyZm4d/3fgG/fucQXtjWjKc3NWLzsW489o2ZKM9JkTo8orhxslI8fvecRyNHp0FTtwNmm1PqUIjGBVaKJwi31w+bM1D5yqqN2GFM1eCS8kCF4Ru722BzxnYb9aNmGx6pPYp1dZ1wef3IS9PghjmFuG9RJa6oMsR1QhwInL67vCJQLb71RHfM/3kQSeFgcDxCilqBdC3nooVcVpEDANjR2AsrXzuIaJw6OU+crdOHItQ+/YjJBpeXlWNERGydTokmWa3A/1w7FU8vn4OsFDUOm2z46qMb8X97469jJJFUOoNJ8by08dc+HTg5V9xsY6U40VhgUjxBhFqny2UCUpOYxIglV1TlwKjXYMDlxWs7W+EXRalDOo3PL+Lduk48vakR3XY3dGoFrp9ZgO9cWo4LCtIglyVOJVBlrg7ZKWq4vH68/GmL1OEQxZz9wSrxeBiLMJbKspJRlKGF1y9i41HOiyOi8amtN9DaMD99fG5YDVdBehJSk5Tw+EQc6RyQOpy4IooiDrZb8fzWJjz64VE8vakBnzb0wOvzSx0aEY2CxRZKirOYgxLLZRU5eOfeS3BhWQbsbh++9+JuPPBWHXz+2NsDJIo1471S3KAP/NyhwwFEFF1MiieIbnvgxiJdq4IsgRKYiUApl2HZnEIo5QKOmgew6VhsJVP6Bz14csMJfHykCwAwtzQDK784CTOL0yFLwISYTBBwycQsAMBTGxvg4cYa0SlCrdOZ8DiVIAjhavFtDd3hw2hERONJe18wKT5OqziGSxCEcAv1A+2cKz5UdpcXT25swPPbmnCww4r2fieOmgfwxp42/OWTE1yDieJYV7BSPFvHSnFKPDk6DZ6/owYrLisHADy9qRHf/fsu7jsRncOAy4sBV6D7rVE/PpPiocMAPXY3XB52lyKKNibFE0R3cC4TT9vGJoNeg6un5gEA3qszoTXYelJqFpsLaz8+jqYeB9QKGW6YU4gl0/OhVsqlDi2qphemIVmtQHu/Ex8cNEkdDlFM2d/WB4AJjzOZZEhBXpoGHp+IpzY2SB0OEdGYa+8b360NR2LKCOeKj1dtfYNY+/FxNFjsUMoFzJ+YhVsuLMaiagOSlPLw13uZGCeKSycrxZkUp8SkkMvwo0UV+NONM6CSy7CurhNPbWyAw+2VOjSimGS2Bu4vklVyJKsVEkcjjRS1AnqNAiKATiurxYmibXy+0iSg0Gn5DM4Tj1lzStJx1GxDXbsVf9/WjNsuLpF0RndnvxNPbWrAgMuLrBQ1bp1XjMxxcmOqlMswpzgd64904fltTbhqaq7UIRHFBIfbi2PmQHtXJsVPF6oW//u2Zjy7uRF3zS/jyBIiGlfagpXiTIoP3ZR8PQDgAJPi5+X0+HDXszvQbXcjTavEbfNKkBOsGKrM1WNaYRqe3dIIk9WFZzY34u4FE5CkSuzDvESJpitc0DE+9h4o8bywrXnI194yrxjPb2tCU48Df93YgDsvKeO6RfQ5oTnahnFaJR6Sm5oEq9OGjn4nijOTpQ6HKKExKZ4gLMEWVOMlqRmPBEHAdTMKYLIeh2XAhbv/thMvfutCqBUje0M8nDfin9fa68DTmxox6PEhN1WD5ReXImWcncabU5qBT452YdOxbhzvGsCE7BSpQyKS3MF2K/wiYNCroWey94yqcvXI0alhtrnw3OZG/McVE6UOiYhoTPj8IkzBygUenBq6UPv0+g4bPD4/lHI2azsTURTx328ewMEOK5JVcnzrC2VI05564DtNq8JtF5Vi7cfH0TXgwpt72nDj3CKJIiaikQjtXWWxfTqNA2XZKfjW/Al4amMDOvqdeGZzA+64pAwqBd8LEIWEkuLjfaxGbqoGh002dPQPjsn3G01egSjecRVOEKFK8UxWise0JJUct1xYDI1Shl3NffivNw5AFMUxjaHBYsdfNzZg0ONDYXoS7rykbNwlxAEgXavC5ZWB+cB8I0AUEJonPjU/TdpAYphMEHBpcLb4Xzc1wO5iGzwiGh+6bC54/SIUMmHcb1oNR1GGFjqNAm6fH0dMNqnDiVkvfdqC13a2QiYAN8wtOi0hHpKapMQ3aoogEwIt6fe29I1toEQ0KqGkeDYLOmicMOo1uP3iUiQp5WjpHcRru1rhH+N9QKJYFmqfnjPeK8WDh447+tk+nSjaxl8mLEGFZoozKR77snRq3DinCM9uacRrO1tRlp2M71xaPibf+6jJhue3NcHjE1GalYxbLixO+Pnh53JTTRE+OGTGG7vb8OPFlTytS+PeoQ4rAGBynl7iSGLb1PxUbG/oRmO3A3/b2oS7F0yQOiQioqgLtU436DWQywSJo4kfgiBgSl4qtpzoRl2bFZPzUqUOKebsa+3Dz/9ZBwD40aIKpCWd+562IF2LyypyUFtvxlv72jHRkAKtilsbRPGgK1wRyL0rGj+MqRp888JiPLWxAQfa+rFerwkXaRCNd6F1IWecH7rNTQ0cCujsd8LnF3m/FcdGWnx3Uw07YI0VZoASRHdopngKbyziwUSDDj/7cjUA4HfrDuPF7dGvVD7Y3o/ntgYS4pMMKbjtopJxnRAHgPkTs5GVokaP3Y31h81Sh0MkuVAFW6VRJ3EksU0uE/DdywKHmf68/jj6Bz0SR0REFH3twaQ4W6cP39SCQCJ8P+eKn6bX7sY9z++C2+fHF6sNuGeIB80urchBjk4Nh9uHDw6ZohwlEUWCKIon26ezUpzGmdKsZHx1eh4A4MN6E5q77RJHRKPx2GOPoaSkBBqNBjU1Ndi+fftZr62rq8PXvvY1lJSUQBAEPPzww6N+zkRiZlIcAJCRrIJKLoPXf3KtJKLoiImk+HBf9F999VVUVlZCo9Fg6tSpeOedd0675tChQ7jmmmuQmpqK5ORkzJkzB83NidsiudsenCnOSvG4sfziUtxzaWDT57/e2I939ndE7XvtaenFC9ub4fOLmJynxzcvLOY8QwAKuQzXzcwHALy2s1XiaIik5feLOGIaAABMYlL8vK6dkY/ynBT0D3rw+CfHpQ6HiCjqQknxvLTx3dpwJEIdWOramRT/LJ9fxL0v70Fb3yBKMrV48OvTIAhDq4qRywR8ZVogubDtRM+YzV8kopGzu31wevwAmBSn8Wl2SQamFaTCLwIv72iB2+uXOiQagZdffhkrV67Ez3/+c+zatQvTpk3DokWLYDafudjG4XCgrKwMv/nNb2A0GiPynInEbAu1Tx/f64JMEGAMVouzhTpRdEmeFRvui/7mzZtx44034o477sDu3buxZMkSLFmyBAcOHAhfc/z4cVxyySWorKzE+vXrsW/fPvzsZz+DRpO4GzjhmeK8sYgr/7moAjfOLYJfBO59aTc+rI98lcP2hh68uqMVfhGYWZSGG+YUQSGT/J9+zLh+VgEA4MN6c/jfEdF41NLrwKDHB5VChpLMZKnDiXkKuQz3LaoAAPx1Y0N4DhYRUaI6mRRnpfhwhVqmH+qwweeP/zmiFpsL2xq68W5dJ3Y19Y64Y8ojtUfxyZEuaJQy/Pmbs6DXKIf1+AnZKZiSp4cI4K29HRA5o5UoplmC1YBJSjmS1Rx5QOPTV6fnIy1JiV6HBx/WJ37CMxE99NBDuOuuu7B8+XJUV1dj7dq10Gq1eOqpp854/Zw5c/D73/8eN9xwA9TqM+/bD/c5AcDlcsFqtZ7yEY/M1lCleOLmbYYqN5wU52FPomiSPDM23Bf9Rx55BIsXL8Z9992Hqqoq/OpXv8LMmTPx6KOPhq/5r//6L3zpS1/C7373O8yYMQMTJkzANddcg5ycM89rSYRFpCc4UzyDleJxRRAE/L8lU3D1Bbnw+ETc9dxOvLE7MhXLoihi/WEz3tzTBhFATWkGrptZwJkknzPJoMPkPD28fhH/PhC9an2KHLapio76zkDr9Ik5KXydGKIrqw2YWZQGp8ePP354VOpwiIiiqq0vcPiHSfHhK81KhlYlx6DHhwbLgNThjJjX58e6A514uPYI/rmnHR8f6cJru1rx+3fr8drO1mElx9+t68QfawNr5+rrpqIqVz+imK6amgulXEBjt53t6YliXNdAaJ44izlo/NIo5eFOJxuPdcHEw9Vxxe12Y+fOnVi4cGH4czKZDAsXLsSWLVvG9DlXr16N1NTU8EdhYeGIvr/U2D79pLzUwH0WK8WJokvSpPhIXvS3bNlyyvUAsGjRovD1fr8fb7/9NiZNmoRFixYhJycHNTU1ePPNN88aR7wvIi6vDzaXFwCQlcwFJN7IZQIeXjYd187Ih88v4gcv78VTGxtG9Zw+v4jXd7fhvYOByvP5E7NwzbQ8yIbYjnC8uSZ4Q/J/e9oljoTOh22qoudIMClewdbpQyYIAn68uBIA8NL2FjRYOBeOiBJXqGKB7dOHTy4TwknfA23xdwAbAPyiiJc+bcEnR7vgF4GSzGTMKclAYXoS/CKwq7kXD39wBFtOdJ+3Gn5HYw++9+JuAMAt84px7YyCEceVrlVh/qRsAMA7+zvg8vpG/FxEFF2hSvGsFBZz0PhWlatHlVEHvwisO9ApdTg0DBaLBT6fDwaD4ZTPGwwGdHaO7M9ypM+5atUq9Pf3hz9aWlpG9P2l5PT4wocqWSkO5Abvszr6BtkBiSiKJE2Kj+RFv7Oz85zXm81mDAwM4De/+Q0WL16M9957D9deey2uu+46fPzxx2d8znhfREItnxUyAfoktqCKR0q5DA8unYbbLy4FAPzyXwfxszcPjGhTx+H24pnNDdjZ1AsBwFcuyMXiKblDns83Hn05mBTf3tiDTp7Gi2mx0KYqEbqLnEm9KZgUNzApPhw1ZZm4rCIbXr+I1e8ckjocIqKoCbVPz01lpfhITInzueLrDnTiYIcVCpmAb9QU4Vvzy3DtjHzcc2k57lkwAYXpSXB5/XhrbzuuX7sZ9Z1nfn9Ue8iEW57aDpfXjysqc3D/l6tHHdv8idlI1yphdXrxEVvREsUsy0AoKc5iDqIvTc2FTAAOm2xo6ubhaho+tVoNvV5/yke86QoellIpZMxpADDoNRAA2N0+2JxeqcMhSliSt0+PNL/fDwD46le/ih/84AeYPn06fvKTn+DLX/4y1q5de8bHxPsi0v2Z1ulMfMYvmUzAz75cFZ5R+7etTbjufzfjYPvQE25HTTb8sfYojnfZoVLIcPO8YsybkBWtkBNGfloSZhenQxSBf+1jtXisipU2VfHeXeRsWCk+cj/9UhUUMgHvHTRh/WFuxhNR4nF6fOh1BKo48pgUH5HQXPF4rBRvsNix8ZgFAHD9rILwzxJSmKHFtxdMwDXT8qBWyLC7uQ9f/uNG/OzNA9jb0of+QQ/q2vvxk3/sw13P7YDD7cPF5Zn4000zoJCPfktCKZfhKxeEWtFaYGYrWqKY1BXcu8pii1wiZKaoMbMoHQDCXR4p9mVlZUEul8NkOvXPzGQynbU7oRTPGS/MtsB7thydmjkNBN7ThkaMcK44UfRImhQfyYu+0Wg85/VZWVlQKBSorj71xHlVVRWam5sjGH3s6LZznniiEAQB372sHE/dNhupSUrUtVtxzaMb8dM39qOlx3HWx7X1DeL5rU14enMjrE4vslJU+Pb8MlQa4+uAh5SuviAXAPBeHW9GYlWstKmK9+4iZ+L2+sOtvyexUnzYJhp0WH5xCQDggbcOsnUrESWc0LxLjZJVHCM1Of9kpXg8tUP0+UW8tTdwaHROSTouKEg743UyQcCFZZn4/sJJWDTZAK9fxN+2NuGrj23CtAfew9V/3IiXPm2BXwRunFuEZ5bPhVYVub9Llbl6VAZb0b61rz2ufo+JxotQpXg2K8WJAACXV+ZALhPQYLGjmdXicUGlUmHWrFmora0Nf87v96O2thbz5s2LmeeMF2Yr54l/Xm5qsIU6O5kSRY2kSfGRvOjPmzfvlOsB4P333w9fr1KpMGfOHBw+fPiUa44cOYLi4uII/wSxoZstqBLO5ZUGvL9yPq6aYoTXL+KFbc34wu8+wvV/3ozfrqvH37Y0YsuJbryzvwN/+vAoHvvoGA52WCETgHkTMrHisolsbTlMX6wOJEV3NPWERxIQnUm8dxc5k+YeB7x+EckqefgNOA3P966YiGydGg0WO57c0CB1OEREERUaL2PUa1jFMUITc3RQygVYnV609sZP5cfOpl50Wp1IUspxZfX5q5VSk5T4y82z8fc7a/ClqUakJikBADqNApdVZOMf98zD6uumQhmBCvHP+/IFeVDIBBzvsuPt/R0Rf34iGp1Qm1xWihMFpGlVmF6YBgDhjiwU+1auXIknnngCzz77LA4dOoR77rkHdrsdy5cvBwDccsstWLVqVfh6t9uNPXv2YM+ePXC73Whra8OePXtw7NixIT9nojLbQklx7kOFhPbzQ6OriCjyJD/mv3LlStx6662YPXs25s6di4cffvi0hSQ/Px+rV68GANx7771YsGABHnzwQVx99dV46aWXsGPHDjz++OPh57zvvvuwbNkyzJ8/H5dddhnWrVuHt956C+vXr5fiR4y6HlaKJ6QcnQZ//uYsfNrYgz/WHsXGYxbsaOrFjqbe066VCcDU/FRcWpEDg55vJIbqhW2ndo/ITdWgo9+J/3n7EGYWpw/pOW6qKYpGaHQGbFMVPSe6BgAApdnJTHaMkE6jxE+/VIkfvLwXj354DNfOyEdeGg8nEVFiMIU2rPg+c8RUChkqjDocaLOirr0fhRlaqUM6L78o4pOjXQAC1WzJ6qFvH1xcnoWLywNjnBxuLzQKOWSy6L7HyEhWYcGkbNTWm/Hzf9ZhbkkG/84SxZCTleLcuyIKubg8CzubelHXbkWv3Y107u3GvGXLlqGrqwv3338/Ojs7MX36dKxbty7cgbC5uRky2cnDf+3t7ZgxY0b412vWrMGaNWuwYMGCcK7ifM+ZqMLt0/U8LBUS2kdqY1KcKGoknym+bNkyrFmzBvfffz+mT5+OPXv2nLaQdHScPOV90UUX4YUXXsDjjz+OadOm4bXXXsObb76JKVOmhK+59tprsXbtWvzud7/D1KlT8eSTT+If//gHLrnkkjH/+caCJTiXKZM3FglpTkkG/nZHDTb++HL85rqpuKmmCIsnG1Gdq8e8skx8fXYBfnJVFZbNKWJCfJSqcgMVvwc74m/W43jANlXRcyLYOr0sK0XiSOLbkun5mFOSjkGPD//95gG2biWihGH6TKU4jdzk3PiaK36w3YoeuxtJSjnmlGSM+Hm0KkXUE+IhCyZlIzdVg267Gytf2Qufn2sxUaywsMsh0WmMeg3Kc1IgAth6olvqcGiIVqxYgaamJrhcLmzbtg01NTXhr61fvx7PPPNM+NclJSUQRfG0j88X753rORNVqH0697NPKkgPJMV7HR4MuLwSR0OUmCSvFAcCL/orVqw449fOVN29dOlSLF269JzPefvtt+P222+PRHgxr8ceWEAyeZowoeWnJeGGuUW4Ifjrz1c50+hV5erxYb0ZR802eHz+qLR1pNEZbncRt9uNgwcPhv8/1KYqJSUF5eXlQ3rO8eC4OVApXpadLHEk8U0QBPzPtVNx9R834sN6M17f1YavzSqQOiwiolHrDM4UN3LExqhMydfj5R2BueLxYEOwSvzCskyoFPHxvlghl2HZ7EKs/eQ4Nh6z4Bf/V4dffnUyO+EQSUwURVhsgYKObLZPJzrFvLJMHDMPYFdLH66cbIR8jA6SEUkt1D6d68JJGqUc2SlqdA240NbrQIUx/kc2EsWa+LizpXPqDleKcwEhGo28VA1Sk5Tw+MRwkpBiy3C7i4TaVM2YMQMdHR1Ys2YNZsyYgTvvvHPIzzkehCrFJ2SzUny0Jhp0uHfhRADAA2/VwRRMJBERxbNQUpxVHKNTnResFG+P/Urxjv5BtPQOQi4ImDchU+pwhiVHr8GDS6dDEIC/bW3CA28dhMfnlzosonHN7vZh0OMDwEpxos+bZNAhRa2A3eXF4c7Yf49AFCknZ4pzXfiswoxAtXhLL1uoE0VDTFSK0+h0c6Y4UUQIgoCqXB22nujBoU4rKnN5Gi8WDae7SKhN1WieczwIzRRnpXhkfHt+GdYd6MT+tn789PX9ePLW2VKHREQ0KmyfHhlVuTrIBKDL5oLZ6ozpede7mnoBAJW5gY36eHP1BbnocUzBz948gGc2N2J3Sx++d3k5LpqQBY1Shj6HB4c6rKhrD8x4P9hhhdvrR45eg6umGOH3A0kqudQ/BlHCsAQTH0lKOZLj8DWFKJrkMgEzi9LwyVELdjT1hg/RESW6rtBMcV3svieWQkG6Frua+9Da65A6FKKExHeiCaCb7dOJIqYqV4+tJ3pQ32GDf7oIGVstUoLrtbvR6/AAAEqzmBSPBIVchjVLp+HLf9qA2nozXt3ZKnVIRESjYrKF2qezimM0tCoFyrJTcMw8gLp2a8wmxb1+P3a39AEAZhenSxvMKNx8YTGyU9S479W92NvShzue3QEAUMllcJ+lcryx24HtDT3QaRT4xtwiFGXyvRFRJITnieu4b0V0JjOL0/HJUQuOmGwYcHnj8kAa0XB4ff5woV+OnvcYnxWaK97aOwhRFDkGiCjC2D49AfSwfTpRxJRmJUOtkMHm8qKVbWpoHDhhCVSJ56VqoFXxxjtSKow6fH/hJADAL/6vLlwdQ0QUb0RRhMkaam0Ym0nceDIlL9CJaH9b7M4VP9xpg8Ptg06jQHmOTupwRmXxFCPeWzkf35pfhqyUQDIulBAvzEjC4slG/OjKSXj6tjl49e55eOCaySjNSobN6cUTGxpwxGSTMnyihBFKimdz34rojHJ0GuSnJcEvAnXtsfsegShSLANuiCKgkAnI0PLA1GcZUzWQywQ43D70BA8OEFHkcPc7zjk9PtjdgblMbJ9ONHoKmQyTDDrsb+tHfYcVRRlaqUMiiqrj5sA88TLOE4+4uxdMwMajFmw50Y2XPm3G3QsmQCHneUQiii+9Dg/c3kASkTPFR29qQRre3NOOfa19UodyVvtaA5vx0wvSIJfFf2VKbmoSfvqlKvz0S1Xotbvh8PiQoVWdsT36nJIMXD+rAEvXbsHBDite/rQF372snPfaRKPUFSzm4DxxorObmp+Ktr5B7G/rR01pptThEEWVOdiJKitFDVkCvN+MJIVMhvy0JDT3ONDc42AhJFGEcWc2zoXajCjlAvQannEgioQKQ6Ai5lhwzjJRImvsDiTF2To98uQyAX9YNh3pWiXa+51476BJ6pCIiIatMzhPPDNZBZWCt4+jNb0wMCd0T0s/RFGUOJrTDbp9qO+0AgCmFiTeTNP0ZBXy05LOOS88Wa3ADXMKUZiehEGPDy9ub4Y/Bv+siOJJly3UPp0b+0RnMyU/sO42dNlhc3okjoYousyhTlRsnX5GxcEircZuzhUnijTuasS57mALqoxkFedLEEXIhJxAxWxb7yAGg50YiBJVc0/gDXZxJrsiRIMxVYPfXz8NALDxmAX1HVaJIyIiGh6TNZAUZ5V4ZEzOS4VCJsAy4EJ78MBBLPnosBken4h0rRL5aUlShyMZhVyGm2qKoVbI0NY3iN3NvVKHRBTXwjPFWe1GdFYZySoUpCdBBFDXzvtGSmxmW2g8E9eFMynODBSuNAULWYgocpgUj3OhSvHMZC4gRJGSmqREtk4NEcBxVotTgmsJJsULOSogahZWGzBvQqD93Ss7WzgTiojiSigpbkxlUjwSNEo5KnMDXYn2NPdJG8wZvL2vA0Cghet4P3SdmqTE5ZU5AID36kxweXhYNtE99thjKCkpgUajQU1NDbZv337O61999VVUVlZCo9Fg6tSpeOedd8567d133w1BEPDwww9HOOr4YAkmP7KZ/CA6p8m5egAId20hSlSh9unZOt5jnElRsHDFbHPB4fZKHA1RYmG/7TjXHZzLlJnCGWdEkVSek4IumwvHugbCLayIElFTMClexKR4VF01xYjWHgdaegfx921NuHvBBCg5X5yI4kBnuFKciYxImVaQhgNtVuxt7cPVF+RKHU6Y0+PDh/VmAOD736B5EzKxvaEH3XY3tjX0YP6kbKlDoih5+eWXsXLlSqxduxY1NTV4+OGHsWjRIhw+fBg5OTmnXb9582bceOONWL16Nb785S/jhRdewJIlS7Br1y5MmTLllGvfeOMNbN26FXl5eWP148ScUKV4NveuiM6pMlePdw+acKLLDofbC62KW/eUmFgpfm4pagWyUtSwDLjQ3O1AZfDADCWuF7Y1j+hxN9UURTiSxMfd2DjXYw8sIJnJvLEgiqTy7EAL9eNmVopT4uof9KDPEZhVxkrx6FLIAm1Yk1VydPQ78c897TE5S5aI6PPYPj3yphemAQD2tPRJGsfnbTnRjUGPD3qNYly3Tv8shUyGBcFE+JYT3fD5uXYnqoceegh33XUXli9fjurqaqxduxZarRZPPfXUGa9/5JFHsHjxYtx3332oqqrCr371K8ycOROPPvroKde1tbXhP/7jP/D3v/8dSqXynDG4XC5YrdZTPhJFF9unEw1Jjk6NdK0SXr+ITce6pQ6HKGrMwXsMzhQ/u5JMzhUnigYeN4tzoUrxDLZPJ4qosqxkyITAiIJeuxvpPHhCCSjUOj0rRYUUNd8SRFtqkhI3zC3CUxsbsKu5F0UZWswtzZA6LCKic+oMzr02MikeMaGk+P7Wfnh9fihipHPIh4cCVeIVRv24b53+WdML0/DeQRP6Bz3Y39aH6YXpUodEEeZ2u7Fz506sWrUq/DmZTIaFCxdiy5YtZ3zMli1bsHLlylM+t2jRIrz55pvhX/v9ftx888247777MHny5PPGsXr1ajzwwAMj+yFinMUW2LvKSlGPuBKKaDwQBAGVRj22nOjGh/UmfLHaIHVIRFFxslKc9xhnU5yZjB1NvWiwsGCLKJK4Ax7nwjPF2YKKKKLUSjkK07Vo6nHgWNcA5iQzcUWJh/PEx96E7BRcOdmId+s68da+duSlaVCQzt9/IopdndbAhpWBM8Ujpiw7BSlqBQZcXhw1D6AqBtohiqKI2kMmAECVUSdxNLFFIZfhwrJMfHDIhM3Hu4eUFB9t0o9tEMeWxWKBz+eDwXBq8slgMKC+vv6Mj+ns7Dzj9Z2dneFf//a3v4VCocD3vve9IcWxatWqUxLtVqsVhYWFQ/0xYpbd5cWgxwcAyGKbXKLzqjTqsOVEN2oPmSGKIg+qUUIyW9k+/XzKspMBAG19g3B6fNAo5RJHRJQYYuNIOo1YTygpzipWoogrzwm0UD/GFuqUoDhPXBrzJ2ahOlcPn1/E81ubYB30SB0SEdFZhdqns1I8cuQyARcUBGZ2742RFuqHOmxo73dCo5RhQvA9MJ00tzQDMgFo7R0M/5sgOpedO3fikUcewTPPPDPkhJZarYZerz/lIxGE5oknKeVIVnFDn+h8SrOSoZQLMNtcOMr9KEpAfr8YXhs4ouns0rUqZCSr4BeBxm671OEQJQwmxeNcd3AByeRcJqKICyXFj3cNwM/Zv5SAmoNJ8WImxceUIAi4flYBsnVqWJ1e/G1rE9xev9RhERGdxuX1hQ/hcsMqsqYFW6jvbe2TNI6QD+sDVeIXT8iCMkbauceSFLUCFYZABf3u5l6Jo6FIy8rKglwuh8lkOuXzJpMJRqPxjI8xGo3nvH7Dhg0wm80oKiqCQqGAQqFAU1MTfvjDH6KkpCQqP0esCiU+snQqVrwSDYFCLkNJZqBCdONRi8TREEVej8MNr1+EIATG+dHZTQhWi5/oYlKcKFJ4txvnQu3TM1gpThRxBelaqBUyONw+dPSzIoQSD9unS0ejlOOWC4uhVcnR1jeI13a18vANEcWcUFtDlUKGdK1S4mgSy7SCNADA7uY+SeMI+SA4T/yKKs4uPZuZxYG26btb+uDzc81OJCqVCrNmzUJtbW34c36/H7W1tZg3b94ZHzNv3rxTrgeA999/P3z9zTffjH379mHPnj3hj7y8PNx333149913o/fDxKCu4NzYLBZzEA1ZqEhj4zEmxSnxhO4xMpNVUPAw5jmVZZ8s2CKiyOCrTpzrHggkxXmqiijy5DIBpVmBE3nH2bKKElAz26dLKjNFjW/UFEMuCDjQ1o/3D5rO/yAiojEUahNt0KtZ3RdhM4rSAABHTDY43F5JY+myucIV61dU5UgaSyyrMOqgVclhc3pxghuTCWflypV44okn8Oyzz+LQoUO45557YLfbsXz5cgDALbfcglWrVoWvv/fee7Fu3To8+OCDqK+vxy9+8Qvs2LEDK1asAABkZmZiypQpp3wolUoYjUZUVFRI8jNKpSu8b8WkONFQTQgmwrae6IbHx65ilFjMtsA9RraOnajOpyy4L93R78SAS9p7BqJEwaR4HHO4vRj0+ACwUpwoWkJJcc5uoUTj9fnR1jsIACjKZFJcKqVZyVgyIw8A8PGRLmw42iVxREREJ3VynnjUGPQaGPUa+EXgQJtV0lg+qjdDFIGp+alsk38OCpkMU/IDs+APtPdLHA1F2rJly7BmzRrcf//9mD59Ovbs2YN169bBYAh0T2hubkZHR0f4+osuuggvvPACHn/8cUybNg2vvfYa3nzzTUyZMkWqHyFmWYKV4tk6JsWJhsqYqkFGsgoOtw97WvqkDocooszBdSGH68J56TRK5KYG3p8fNdkkjoYoMSikDoBGLlQlrpLLkKLmHyVRNITmODV1O+AXRchYJUUJoqPfCa9fhEohg4GncyU1qzgDA04v3j1owr8PdEKjkOOmmiKpwyIiQmd/qFKc60Q0TCtMRWedE7uaezG3NEOyOGqD88Qvr2SV+PlMyUvF9oYe1LVbcc00EXIZ7w0SyYoVK8KV3p+3fv360z63dOlSLF26dMjP39jYOMLI4lt4pjgrxYmGTCYIuGhCJv61rwObjlkwp0S69wlEkdbFpPiwTDLo0NHvxGGTDTOK0qUOhyjusVI8jvUE54lnpqjYzpAoSvLSkqCUCxj0+MInGYkSQah1emF6EmTc0JXcgooczJ+YDQB4c08bXt/VKnFEREQnqzhYKR4doQ3uTxt6JIvB6fFhw9HAvNKFnCd+XqVZydCq5HC4fewkRTREoeRHNsf+EQ1LTVkmAODTRuneJxBFgznYjSpHz6T4UFQadQCAo6YB+PyixNEQxT8mxeNYtz1wY8HW6UTRI5cJ4XnLTdz4ogTS1M154rFm0WQD5pZkQASw8pW9+PP64xBF3vAQkXRYKR5doerw7Y09km1wbT3RDYfbB4NejSn5ekliiCdymYDq3MDv04E2tlAnGgpWihONTE3wfcLOpl64vZwrTonjZPt03mMMRWGGFklKOQY9PrQEC1yIaOSYFI9jlmD7dN5YEEVXqIV6o4VJcUocoUpxJsVjhyAIuGZ6Hi4pzwIA/HZdPe7/Zx1PAhORZEIzxQ2p3LCKhupcPVLUCticXhzulGZG4If1ZgDA5ZUGdh8bosl5gaR4faeNh9eIhiC0d8WZ4kTDU56dgnStEk6PHwfaeRCLEgdnig+PTBAwyZACIPD+k4hGh0nxOBaaKZ7JFlREUVUcSop38zQeJY7Q6dKi4N9vig0yQcCXpubiZ1+uhiAAf9vahJv/ug2mYGJKaj6/CK+PVQpE40XotYft06NDIZdhVnFgLuD2hu4x//6iKKL2UCApfgXniQ9ZWXYKlHIB/YOe8MERIjo7VooTjYxMJoRHrWyXcNQKUaSZbWyfPlyVwU5Fde39PJRJNEoKqQOgkeONBdHYKMrQQiYA/YMe9DrcSNfyIArFP1aKx7Y7LilFbqoGP3xlLzYf78aihz/Bf32pCtfPKoh6Jd+g24fDJhsaLAPosbvR6/Dggbfq4Pb5Ebr3UilkSFErkKyWI1mlQIpaAX2SEpnJKmSmqNHc40CKWoEcnRo5ejUUMp7DJIo3oiiG26czKR49c0sz8PGRLmxv7MFtF5eO6feu77ShrW8QaoUMFwe7lND5KeUylGWl4LDJhiOdNuSmJkkdElHMsru8cLh9AIAsVgQSDdvc0gy8d9CE7Q09uHvBBKnDIRo1URRhtrJ9+nBVGnVQyAR0293o6HciL43vP4lGiknxONYdToozQUcUTSqFDHlpSWjtHUSjxY70Iv6bo/jX1B0YB8CkeOz60tRcTDLo8L0Xd+NghxX3vbYPz29twve/OAkLJmZDJotccrzH7sahDisOdVjR2G3H+Tq2u71+9Hjd6BnCVAm5IMCYqkF5Tgqm5KUiL03DFr1EccA66IUrOL+SVRzRE5oXuu1ED/x+MaKv7efz/kETAOCS8iwkqeRj9n0TQYVRh8MmGw6bbFhQwSp7orMJtcjVquRIUXMLkmi4akozAQA7Gsf+fQJRNFidJ+8xOFZj6NQKOSqMOtS1W7G/rZ9JcaJR4DvSOBaay5SZzAWEKNpKMpPR2juIpm4HZhSlSx0O0aj0OzywOr0AmBSPdeU5Kfjniovx140N+GPtUext7cfypz9FUYYWN9UU4doZ+TCMoILTL4po6x0MJMI7rTAFT2qH5OjUqDDqYNBpkJ6swi3ziqFWyqAMVnzb3V7YXT4MuLywBz/6Bz3otrthGXBhZ1MvrINedFoH4fT40dY3iLa+QXx8pAt5aRp8oTwbUwtSIWNynChmhdpCp2mV0CiZMI2WCwrSkKySo9vuxsEOK6bkp47Z937vYCcAYNFk45h9z0RRYdQBewOddwbdPh4qIDoLc3At4dxYopGpzNVBrZDB6vTihMWO8pwUqUMiGpXQuqDXKHiPMUxT81PDSfErqw0sNiAaISbF41i4fTpvLoiiriRTi43HgMbuIZRFEsW4UOv0bJ2am7hxQCmX4e4FE3D9rAL8ef1xvLKjBc09Dvzm3/X4zb/rUWnUIStFjcIMLXJ0amQmq6CQn2xXLooiXF4/euxuNPc40GCxo8Fix4DLG75GJgQO/1Tl6lFp1CHzc6NZCj93eCI9+dwdM17Y1hz+3r0OD5p7HOFK9PY+J17e0YL1R8z46rR8lGRxrj1RLOrkPPExoVLIMG9CJj44ZMaGo5YxS4q39jpwoM0KmQBcUcVK5+FK16qQo1PDbHPhqNmGCwrSpA6JKCaFKsXZIpdoZJRyGS4oSMWnjb3Y09LHpDjFvfC6wHuMYasw6qCUC+ixu9HSO8giF6IRYlI8jp2sFGcrZ6JoK84MJG3MNhccLi+0bP1GcYzzxONTVooaP/tyNX50ZQXe2teOl7Y3Y3dLH+o7bQBs4esEABqlHAq5ALlMgMPtgzvYnuyz1AoZJhl0qMrVo8Kgi8oBCUEQkJGsQkayCtML0+BwebG1oRubjnXDZHXh8Q0nMLs4HVdNMZ430U5EY8sUnCc+km4UNDxfmJgdTIp34Z5Lx2ZeaKh1+uySjNMOQtHQVBh1MNtcONzJpDjR2ZxMfvB1hmikphemBZPivbh+VoHU4RCNitkWusfgujBcaoUcU/JSsbulDzsae7inRzRCsvNfQrHI7xfRYw/cXHD+BlH0JasV4X9rjd0OiaMhGp2W3sDf4cJ0ziCKR0kqOb4+uxCvf+di7PzvL+JPN87A7OJ0FKQnQa2QQQQw6PHB5vSiz+EJJ8STlHJMyE7Gwqoc3PmFUvzX1VW4cW4RphemjVnHAK1agcsrDfjhFydhdnFgFMWOpl588Q+f4MN605jEQOPLY489hpKSEmg0GtTU1GD79u1nvbaurg5f+9rXUFJSAkEQ8PDDD4/6OeMZK8XHzhcmZgEAdjT2wuH2nufqyHivLvCae2W1YUy+XyKqMOoAAIdNNvhFUeJoiGLTyfbpXEuIRmp6YeC+aU9Ln7SBEEWA2coOIqMxqyTwerCvrR8ur0/iaGKf1enBzqYevLG7FU9sOIFHao/gsY+O4fmtTfjosDn8PoXGF5Y6xqlehxv+4H13BiuriMZEcYYWXTYXWnodqM7TSx0O0Yi19Q4CAPKZFI97GckqfGVaHmzBGfGiKGLA5YXT44fH54fXL0KrkkOvUUKliJ2zkFq1AtfNLMDMonS8uacNZpsLtz+zA9+oKcJ/XV0FrYpvUWn0Xn75ZaxcuRJr165FTU0NHn74YSxatAiHDx9GTs7p7aIdDgfKysqwdOlS/OAHP4jIc8azUFLckMoNq2grzUpGQXoSWnsHsfVEd9S/X6/dje2NPQA4T3w0ijOSoVHK4HD70NY7eNqoESJipThRJMwoSgMAHOqwYdDt4wg0imsnx2pwXRiJ0sxkZCar0G13Y39rv9ThxCSfX8TBDiu2nuhGo8WOMx1dbesbxMEOK94/aEJJphYLqwwoy+Z4ivEidnZHaVi67YHW6WlaJZRy/jESjYXQRldLDyvFKb619wWT4mncvE00giBAp1EiW6dGXloSijK0yEpRx1RC/LNKspLx3cvKccclpQCAv29rxpce2YDdzb0SR0aJ4KGHHsJdd92F5cuXo7q6GmvXroVWq8VTTz11xuvnzJmD3//+97jhhhugVp95k2a4zxnPzKwUHzOCIODSimwAwPsHzVH/frX1Zvj8Iqpy9UzkjoJcJqA8J1AtHhhlQkSfF2qTy+QH0cjlpmqQo1PD5xdxoJ1JMIpvoaQ4O9+OjCAImFOSAQDYfLwbIrsVhdmcHnxYb8Lv363Hi9ub0RBMiBekJ2H+xGx8fXYhll9cglsuLMaXpuaiwqCDTAh0hH1yYwNe+rQZA66x6dpF0orNHVI6L8tAYAHhPHGisVMQrKpt6xtki0SKa23BpHheGhMdJD2lXIaffbkaf7+zBrmpGjR2O3D92i146P0j8PhOn4VONBRutxs7d+7EwoULw5+TyWRYuHAhtmzZMmbP6XK5YLVaT/mIF+FKcVb3jYkrqwMV2+8fNEX9feZ7dZ3B78nW6aNVYQgkxY+amRQnOhO2ySUaPUEQML0wDQCwp7lP0liIRis8VoMHb0dsdkk6VHIZOq1ObDxmkTocSYmiiEaLHS992ozfrTuMDw6ZYXV6kaxW4LKKbPznogp859JyLJ5ixPTCNEzM0aEyV49LyrNw60Ul+M9FlbiwLAMCgH2t/Xj0w6No7WUxXKJjUjxOWQYCleJZKdykIhorBr0GKrkMLq8fXcGTjUTxKNQ+vYDt0ymGXFyehXXfn48l0/Pg84v4Y+1RXP/nzTjeNSB1aBSHLBYLfD4fDIZTk34GgwGdnZ1j9pyrV69Gampq+KOwsHBE31sKnf2B9zoGbliNiQvLMqHXKGAZcKG5O3obMYNuHz452gUAuHIyk+KjNdEQaLPY1jvIyhKiM2D7dKLImFEUmCO8u4UdtSi+dbF9+qhpVYrwbPHHPzkhcTTSGHT7sOmYBY/UHsXjG05gX2s/fKKIogwtvj67ED9eVIEvVhuRpj13Qak+SYlrpuXjnksnICtFDavTi8c/OYF/7mkbo5+EpMCkeJzqDlaKMylONHZkghCewcwW6hSv+gc9sAU3bfPSmBSn2JKapMTDN8zAn26cAb1Ggb2t/fjSIxvwm3/Xo9/hkTo8omFbtWoV+vv7wx8tLS1ShzQkHp8f3fbA/YaRM8XHhEohwxVVgST1wY7odRT4sN4Mp8ePgvQkVOfqo/Z9xgu9RoncVA1EAMfMPMRF9FlOjw/9g4H3bwZWihONCivFKVFwpnhkXDwhCwKADUct2NHYI3U4Y8LnF3HEZMMrO1qw+t+H8Pb+DphtLijlAmYVp+O7l5Xj7gUTML0wDYphjhsuSNfiO5dOQIVBB69fxL0v7cFD7x1me/oExaR4nLKEk+Jsn040lgpDSfFgpS1RvAnNE0/XKqFVKSSOhujMvjItD+/+YD6+MDELLq8faz8+jkt+9yH+VHs03G6N6FyysrIgl8thMplO+bzJZILRaByz51Sr1dDr9ad8xAOzzQVRBJRyARnnOV1PkbMoWLl9oK0/ai3U39gdqHq4ZloeBEGIyvcYbyYG54ofNbGFOtFnhaoBVQoZ9Em87yAajQsKUiETgPZ+J++HKG453N5wZx22Tx+djGQVZgerxf/nnUMJm7z1+UVsOd6NN3e3YfW/D+GZzY3Y09IHr1+EUa/BNdPysOqqKnxtZgHyR1n4o1HKcfO8YsyfmA0A+OOHx/DLfx1M2N/b8YxJ8TjVHWyfnslKcaIxVZCuBQDOF6G4FWqdns/W6RTjclOT8Nztc/HkLbNRYdDB5vTiwfeP4MLVtXhmcwN2NfWie8DFGxQ6I5VKhVmzZqG2tjb8Ob/fj9raWsybNy9mnjNWmUKz/nQayGRMnI6VSytyoNMo0DfoQYPFHvHn77W78fERMwDg2hn5EX/+8WpSsIX6EfNA1OfBE8UTsy20lqh5CIdolJLVCkwyBA5h7W7pkzYYohEyWwOHpbQqOVLUPCw1WldUGqBRyrCruQ//PjCyEWGxyOb04O19HVj5yh7M+fUHuPGJrdje2AOH24dktQIXlmXgngUT8B+Xl+PCskxolPKIfW+ZIGDxFCN+9dXJAICnNzXip28cgN/P9/iJhK8+cSpUKZ7JSnGiMVWYEUiKm6xOuL1+iaMhGr62YKX4aE9QEo0FQRCwsNqAyypz8NbedvxtaxN2NvXiiGkAR0yBNrValRy5qRroNUrok5RIUsohEwCZTIBMECAXBGiUMmiUcmhVchSka1GSqR12Oy2KPytXrsStt96K2bNnY+7cuXj44Ydht9uxfPlyAMAtt9yC/Px8rF69GgDgdrtx8ODB8P+3tbVhz549SElJQXl5+ZCeM1GY+gOJDANnwI4pjVKOL1+Qhxe3N2NXUy8mZKdE9Pnf3t8Bj09Eda4eE4Mb6zR6RZlaqBQy2F1edPQ7+R6LKCiU/GCLXKLImFGUhvpOG3Y392HR5JF1PiKSElunR5Y+SYlvfaEMf/zwGH7+f3W4aELmeWdox6Jeuxt7W/uwo7EXnzb2YFdzLzy+k0lovUaBiQYdphWkoTQrGfIxOLR987wSqJVy/Pgf+/Di9ma4PD787voLuI+UIJgUj1OWYKU4Z4oTja3UJCX0GgWsTm84uUgUT04mxbUSR0I0dHKZgCUz8rFkRj4aLHb86l8HcdRkQ3u/Ew63D8e7zl1R+caetlN+naJW4KIJmfjmhcX4wsQsVi8lqGXLlqGrqwv3338/Ojs7MX36dKxbtw4GQ6BFdXNzM2Sykze17e3tmDFjRvjXa9aswZo1a7BgwQKsX79+SM+ZKDqDleKcJz72rp9VgBe3N+NAez+u8eRBHcHKh1d3tgJglXikKWQyTMhOwaEOK46abEyKEwWdTH5wLSGKhOmFaXhxewv2tPRKHQrRiJzsIMJ1IVK+c1k53t7fgeNddjzw1kE89PVpku9viKIIp8cPm8uDAZcXdpcPTo8PNqcH/YOBD6vTi/a+QZzoGkCvw3Pac5RlJ2NhlQGXV+ZgVnE6Xt3ROuY/x9dnF0KtkGHlK3vx+u42OL0+PLxsBlQKJsbjHZPicYozxYmkU5CuxcEOK1uoU1wKJcXz0ngTQvGpNCtwc7SwygCvz4+OficsAy5YnV5YBz1weX3wi4HZU35RhM8vIlunhsvjh9XpQXOPAwMuL947aMJ7B02YW5qBB5dOC3cCocSyYsUKrFix4oxfCyW6Q0pKSobUjv9cz5koQklxA2f9jbmZRWnISlHBMuDG7pY+XFiWGZHnPdDWj70tfVDKBVw7k0nxSJtkCCTFj5hsuLQiR+pwiGJCOPnBriNEETGtMA0AUNdmhd8vcsQNxZ1QB5FsrgsRo1HK8bvrp2Hp2s14Y3cbZhal4eZ5JWMag9vrx4muATRY7Ojod6K9fxAOt29Yz1GcqcXs4gzMLknHhWWZKM1KjlK0w/PV6fnQKOVY8cIuvLO/E27vTjx608yItmynscekeJzqZqU4kWQKMwJJ8ZYeJsUp/oRmihdwpjglAIVchsIM7XkT2jfVFIX/3+8XUdduxeu7W/HS9hZsb+jBlx7ZgKeXz8Hskoxoh0wUF0Lt041Mio85QRBwYVkm/rWvA5uOWTC3NAOyCFR7PL+1CQBw1ZRc3kNGwcScQDv65h4HnB4fN8qIAJjYPp0oosqzU6BWyGBzedHc40BJjCSNiIaK7dOjY1ZxOv5zcSV+8+96/OKtg8hLS8IVVdHtZOb2+lHX3o/9bf043jVwSrvzEI1ShhS1AskqBZJUclTn6qFPCoy9S01SIlunxoTsZJRmJUOrit005aLJRjxxy2x8+2878cEhM+56bgcev3k2klR8vx+vYvdvG52Vw+3FoCdw2iaTGxpEY64wmExs6WX7dIo/bJ9O451MJmBqQSqmFqRi+UWluPfl3djd3Ifbnv4Uf7tjLmYUpUsdIpHkQokMtk+XxqzidHxwyIRuuxv1HVZU56WO6vn6Bz345552AMA3LyyORIj0ORnJKmSlqGEZcOGYeQBT8kf3Z0aUCMLJDx6wIooIhVyGSqMOe1v7UdduZVKc4o7Zyvbp0fLt+WU40mnD67vb8O2/7cRDy6bjmml5Ef0eoiiisduB3c292N/WD5fXH/5aapISFQYdCtKTkJuahGyd+rQ2458tVog3l1bk4Onb5uDO53Zgw1ELbnt6O/562xykqJlejUdsgB+HLLZAlbhGKUMyT6QQjbn8tCQICGwwht7QEcUDp8eHruDmVD4rxYlQlKnFC3deiHllmRhwefHtv+1Ej90tdVhEkjNxw0pSaoUcNaWBtunrj3QNqa3/uTy7uRGDHh8qjTrMKeHBn2iZZEgBABw12ySOhCg2nEx+sJiDKFImBw9dHWjvlzgSouELHZYysH16xAmCgN9efwG+Mi0PXr+I7724G/f/8wAGXN5RP3eP3Y0PDpmw5r3DeGLDCexo6oXL60e6VonLK3PwH5eX4z8XVWDJjHzMLslAfnpSQs7dvqg8C8/dPhc6tQLbGnpw81+3oX/w9HnoFPsS72/nOGCxBxaQzGQ1hAi00iOi4VEr5eG5aHta+qQNhmgYOoPtcDVKGdK1SomjIYoNSSo5nrx1NspzUmC2ufDjf+wbdQKKKJ6JohieKc5KcelcNCETKrkMrb2D2N828o3vAZcXT21qAAB857Jy3j9G0SRDoIX6EdMA1xEiIHwYlwesiCJncp4eAFDXbpU4EqLhM9t48DaalHIZHl42Hd+aXwYAeG5LExb87iP8ef3xYRV1+UURLT0O1NabsPbj41jz3mF8WG9Gr8MDlUKGWUXpuOsLZfjhlRVYWGVAbmrSuLnHmF2Sgb/fVYPUJCV2N/fhG09uRS8LK+IO6/vjkCV4Y5HF07ZEkilI08JkdeFAWz+unGyUOhyiITnZOn38vGElGopktQKP3DAd1z62Ge8fNCErRY2pbH1L45TN5YXDHRjVxJni0tFplJg/KQsfHDLj3bpOVOXqoZQP/0z7s5sb0efwoCw7GVdPzY1CpBRSmpUMhUwIdJOyuWDgvx8axzw+P7qDm8Q5rAgkipgpwZEqdW39EEWR9/UUV06O1eC6EC1ymYCffqkKF5dn4Rf/V4cGix2/XVeP366rR3WuHhcUpKIwQ4v6DivUCjkgAINuHwY9PvQ5PDBZnTDbnKfMCBcATMhJwcyiNFTnpiZkFfhwXFCQhhfvuhA3/3UbDrRZccPjW/H8nTXIZq4ubjApHodCNxZZySqJIyEav/LSNNjZDBzg6VyKI229waR4OueJE33e5LxU3H3pBPyx9ijeq+tEVa4OCtn4vtmj8ckU7Cqi1yiQxFFNkrqkPBvbG3rQ6/Dgg4MmXDXMpHZH/yAe++gYAOA/Li+HXMaN82hSymUozUrGUfMAjppsTIrTuGYZCCQ+FDIBGVruXRFFSoVRB7lMQLfdjU6rE7mpHItG8cHlDSRdAcDASvGoWzApG+/9YD7e2N2Glz9twc6mXhzssOJgx9D2sTVKGSZkp2BSjg6TjDqkJrHb5GdV5+nx8rcvxE1PbMNhkw3L/rIFf7+rhq/JcYJJ8TgUrhRP4ekTIqnkpwUWudG0syQaa62fqRQnotN9a34ZXtjWDMuAC5829GDehCypQyIac2ydHjtUChm+Oj0ff9vahI3HLJhk1GFCdsqQH/+rfx2Ew+3DrOJ0fHVafhQjpZBJBh2OmgdwxDyASyZmSx0OkWRM1pP7VjIeyCGKGI1Sjok5KajvtKGuzcoEDMUNc3BdUClk0CcxJTUWlHIZvj67EF+fXYgumwvbG3pw2GRDe98g6jttcHt9EMXA60qSSg6dRgGDTgNjqgYZySrI2IninMpzdHjl2/PwjSe34YTFjmV/2YoX7qqROiwaAr4CxaFQpXhmCk/bEknFmJoEAYE5aWarEzmsBKE40B5OivPvK9GZpKgVuHfhRPzszQP45KgFc0szWVlJ404okcEq19hQlavH7OJ07GjqxYvbm3H3/AlDGqP10vZmvLO/E3KZgP+3ZAqTUmNkoiEF2A80WOxwe/3jvr0kjV+h2aUGtsglirjqPD3qO2040N6PhdUGqcMhGpLQPHGDXs22/xLI1qlx9QW5uBqBzlMvbGuWOKLEUJKVHK4Yb+5xYNlftuKGOYXIZDFrTOMdWhwKtaFipTiRdFQKWXhWCKvFKV6cbJ/O0+REZ7N0VgGSVXL0D3qG3FqMKJGYwokMJsVjxZcvyEN+WhIcbh+e2tyArmDnsLPZdqIb9/+zDgDw/SsmoipXPxZhEoDsFDXStUr4/CKOmGxSh0MkmdDc2Gy2yCWKuPBccY7zozgSqhRn63RKNAXpWrzy7Xkoy0pGW98gnthw4rz3ayQtJsXjUCgpzkpxImmFWlAfaOONCMWHtnClOGeKE52NRinH3NJMAMDmYxaJoyEae53BmeJGJsVjhkohw60XlSAzWYU+hwd//vgY9rb2QRTFU64TRRF7W/tw81Pb4fb5sXiyEd+9rFyiqMcnQRAwOZis4MEqGs9CSfEcVooTRdzkvMBhtzoWaFAcCR285bpAiciYqsFL374QE3NSYHV68cSGE+G/8xR7mBSPQ90DgfbprBQnklYe54pTHPH7RXT0B5LieWyfTnRONWUZkAsCmnoc4X83RONFaKa4gTPFY0qKWoFvzS9DcYYWTo8fL3/agv9dfxyfHOnC/rZ+bD5uwRMbGvDypy1we/1YWJWDh5ZNY9t0CVQHK/PrO63w+v0SR0Mkja5gm9ycIYx7IKLhqQ4mxdv7negJjtgkinWm0GEpVopTgsrRafDSty5EbqoGA65AYpz7SbEpJpLijz32GEpKSqDRaFBTU4Pt27ef8/pXX30VlZWV0Gg0mDp1Kt55552zXnv33XdDEAQ8/PDDEY5aOmyfThQb8sKV4kyKU+zrGnDB4xMhlwms/iM6D71GicpcHQBgT3OftMEQjbHQiXauFbFHp1HijktKcUVVDpRyAW19g1hX14kXtzfjX/s60Nhth0Im4LuXTcBfbp4NrUohdcjjUlGmFilqBZwePxq67FKHQySJUJtcJj+IIk+nUaI0KxkAUNfO/SiKD+H26bzHoASWmaLGHZeUhkdfPbmhAe19TIzHGsmT4i+//DJWrlyJn//859i1axemTZuGRYsWwWw2n/H6zZs348Ybb8Qdd9yB3bt3Y8mSJViyZAkOHDhw2rVvvPEGtm7diry8vGj/GGPG6/Oj1+EBwPbpRFLLS9NAEAIVVZwVQrGuNThP3KjXQCGXfPkninkzCtMAAHtb++D/XItiokTGpHhsU8hluKLSgB9dWYFrpuWhOlePkkwtJhlSsLAqByu/OAn3LaqEnBXikpEJQniOex1bqNM4ZWKlOFFUharFOc6P4oWZ6wKNE1qVArdfXIrC9CQMenx4alMDW6nHGMl3xR966CHcddddWL58Oaqrq7F27VpotVo89dRTZ7z+kUceweLFi3HfffehqqoKv/rVrzBz5kw8+uijp1zX1taG//iP/8Df//53KJXKc8bgcrlgtVpP+YhVobY4MgFI1zIpTiQltUKOsuDp3AM8nUsx7uQ88SSJIyGKD5MMOiQp5bA6vTjBSj8aJ7w+f/ign4Hz/mKaTqPEhWWZ+OaFxfjW/Am47aJSXF5pQBrvEWNCaN7roQ4rD1bRuMSKQKLompKXCoCV4hQ/QklBrgs0HiSp5Fh+8cmK8ac2NaB7gAV1sULSpLjb7cbOnTuxcOHC8OdkMhkWLlyILVu2nPExW7ZsOeV6AFi0aNEp1/v9ftx888247777MHny5PPGsXr1aqSmpoY/CgsLR/gTRZ8lOE88I1nF0/9EMWBKfvBGhC3UKca19XKeONFwKOQyTA2+xu9t7ZM2GKIxYhlwwy8CcpmATI5qIhqxsuxkqBUy2JxetPY4pA6HaEz5/CK6gwUdOTxgRRQVocNXB9tjt7CL6LPMPHhL44xGKcfyi0pg0Kthc3rx100N6HO4pQ6LIHFS3GKxwOfzwWAwnPJ5g8GAzs7OMz6ms7PzvNf/9re/hUKhwPe+970hxbFq1Sr09/eHP1paWob5k4ydLs4TJ4opoYTJfibFKcaFZtjkp7NSnGiophYEXuPrWelH40Sn9WRbQx7AJRo5hUyGSqMOAFDHhAWNM102F3x+EXKZwL0roigJjelo6LbD4fZKHA3RuTk9PvQFx8Hm6FioQeOHVh1opZ6VokKfw4O/bmyAzemROqxxT/L26ZG2c+dOPPLII3jmmWcgCEPbyFGr1dDr9ad8xCpzcKMqm/M3iGLC5GDLKs5xolh3sn26VuJIiOJHSWYyNEoZ7G4fWljpR+NAZz/bGhJFSnWotW2HFSIPVtE40tEfuO8w8IAVUdRk69TISlFDFIHDnTapwyE6p9B4JrVCBn2SQuJoiMaWTqPE7ReXIk2rRLfdjac3NcLp8Ukd1rgmaVI8KysLcrkcJpPplM+bTCYYjcYzPsZoNJ7z+g0bNsBsNqOoqAgKhQIKhQJNTU344Q9/iJKSkqj8HGMp1GqEp6qIYsPk/MAhmra+QfTa2QKFYleofTorxYmGTi4TUGEIVPod6uBmEyW+0Kw/I5PiRKM2yZACpVxAj90dPpxINB6EDlgZU7mWEEVTdaiFegeLNCi2mW3BblR69ZCLGIkSSZpWhTsuLkWKWoFOqxMvbG+Gz89Ds1KRNCmuUqkwa9Ys1NbWhj/n9/tRW1uLefPmnfEx8+bNO+V6AHj//ffD1998883Yt28f9uzZE/7Iy8vDfffdh3fffTd6P8wY6eL8DaKYotcoUZIZqLxlC3WKVaIofqZSnJtTRMMRak14iJtNNA6Ek+JMZBCNmlohD68hu1v6pA2GaAx1BJPiuak8jEsUTVW5ocO7vE+h2GayBvMZLPKjcSwzRY1b55VAKRdwzDyAN3e3sZuURCRvn75y5Uo88cQTePbZZ3Ho0CHcc889sNvtWL58OQDglltuwapVq8LX33vvvVi3bh0efPBB1NfX4xe/+AV27NiBFStWAAAyMzMxZcqUUz6USiWMRiMqKiok+RkjKXyyiu3TiWJGqIU6b0QoVlmdXgy4AnPG8tK4OUU0HJMMOsgFAV0DLnQPuKQOhyiqwjPFeQCXKCJmFKYBAPa19LEahMaN0FqSywNWRFFVHT68y45WFNtCB285oonGu/z0JNw4twgCgJ3NvfjosFnqkMYlyZPiy5Ytw5o1a3D//fdj+vTp2LNnD9atWweDwQAAaG5uRkdHR/j6iy66CC+88AIef/xxTJs2Da+99hrefPNNTJkyRaofYUyFTlblcBEhihk8nUuxLtQ6PSNZBa2K85uIhkOjlKMo2BHkWNeAxNEQRRfbpxNFVnmODslqBexuH46ambSg8aGD7dOJxkSoG0l9hxV+HryiGBYaB5vNIj8iVBr1uGZ6HgDgg0NmHO5kPmGsxcTO+IoVK8KV3p+3fv360z63dOlSLF26dMjP39jYOMLIYg8rxYliD+c4UawLtU7PY+t0ohGZkJ2CBosdx8wDqCnNlDocoqgJz4FlUpwoIuQyAdMLUrHpeDd2NPai0qiXOiSiqOsI3nuwfTpRdJVlJUOlkMHu9qG5x4GSrGSpQyI6I1aKE52qpjQTnf1ObGvowSs7WvHdy8qRkaySOqxxQ/JKcRo6URRhDs3g4CJCFDNCp3OPd9nh9PgkjobodG29DgBAPlunE41IeU4KAOBElx1+znyiBBae98fqPqKImV2SAQCo77TCOuiROBqi6GOlONHYUMhlqDCwcyHFvi5bKJ/BIj+ikKun5qIgPQmDHh9e2NYEj88vdUjjBpPiccTq9MLlDfzjYLsRothh1GuQrlXC5xdx1MTWuhR72oMbU/lpWokjIYpP+WlJ0ChlGPT40B6sfiJKNAMuLwZcXgA8gEsUSQa9BsUZWvhFYEdTr9ThEEWV3y+GKwI5U5wo+jjOj+JBaF3I0XFdIApRyGW4aW4RtCo52vud+Ne+jvM/iCKCSfE4Yg4uIHqNAhqlXOJoiChEEIRwtThvRCgWhWaK56ezUpxoJOQyAWVZgWrxY2YefqLEFNqsSlErkKKOiSlbRAljbmmgWnxHYw98nPtKCcxid8HrFyETWMxBNBZCe1EHO2wSR0J0duFuVKwUJzpFmlaFG+YUQQDwaWMP6jlffEwwKR5HzMFWIzms3CCKOdW5nCtOsas1WNmaz5niRCM2ITswo++ExS5xJETRYeoPzfrjZhVRpE3JT4VWJUffoIf3C5TQOoNrSbZODaWcW45E0VbNAg2KcU6PD/3B8THMaRCdrjwnBRdNyAQAvLGrDQ63V+KIEh/focYRs40bVUSxqjovmBRv540IxZ5wpTjbpxONWElWICne3O1glR8lpNAM2NxUdhUhijSlXIYLywKbXRuPdkEUuY5QYjo5T5xrCdFYqAwmxdv6BtHv8EgcDdHpQvPE1QoZ9Bp2oyI6kysnG5GdoobN5cW7dZ1Sh5PwmBSPI+ZgqxHO3yCKPZ9tn85NLoolTo8PloHA+lHA9ulEI2bQa6BRyuD2+dHRz7nilHhCf685A5YoOmpKM6CQCWjpHURzj0PqcIiiIlQpnstqQKIxkZqkRH5a4D7/ENvuUgwKjWgy6DUQBEHiaIhik1Iuw5IZ+QCATxt70dTNDoXRxKR4HDGFk+KsFCeKNROyU6CSy2BzedHay2QJxY72YOt0rUqONK1S4miI4pdMEFCSGagWb2ALdUpA7eFKcSYyiKJBp1FiemEaAODDerO0wRBFyclKca4lRGOlii3UKYaFxsGy8y3RuZVmJWNWcToA4K297fCz6C5qmBSPI6H26dlMihPFHJVChvKcFACcK06xpS08TzyJp3KJRimUFG/sZoUfJZ6O4HqRm8auIkTRcmlFDmQCcNQ8wAoQSkidwa4jeWlMihONFY7zo1gWqhRn51ui81s02Qi1Qob2fif2tvRJHU7CYlI8jpw8WcVFhCgW8UaEYlGoc0E+W6cTjVppcK54o8XOU7uUcDpYKU4UdRnJqnAFyPuHTBJHQxR5nClONPaqc3UA2D6dYlO48y0rxYnOK0WtwKWTsgEA7x00wePzSxxRYmJSPI502dg+nSiWsWUVxaK23pOV4kQ0OnlpSVDKBQx6fLAMuKQOhyiiQomMPK4XRFF1WUUO5IKAE112nOgakDocoojqtPKAFdFYC+1FHTENwMsECsWYUOdbVooTDc1F5VlITVKif9CDTxt7pA4nITEpHkfC7UZYKU4Uk6qDNyJsn06xJNw+nZXiRKMmlwnhhGFLz6DE0RBFjsPtRf+gBwATGUTRlqZVYXZJoFr8g0MmiOw8QglCFMWTleLctyIaM4XpWiSr5HB7/Thh4WgOii1mK2eKEw2HUi7DgmC1+CdHulgtHgVMiseJAZcXDrcPACvFiWJVKCne2jsY3lgmklqoUrwgXStxJESJoTD4b6mll3PFKXG09wWSGDq1AjqNUuJoiBLfpRU5UMgENHY7cMzManFKDL0OD9zewMYtx/4RjR2ZTAhXi3OcH8UazhQnGr7ZxelITVLC6vRiR1Ov1OEkHCbF44Q5uICkqBVIViskjoaIziRVqwy3qK5ntTjFiHClONvhEkVEYUYgKd7aw6Q4JY6O/sBaYWSVONGYSE1SoqY0AwDwbl0n/KwWpwQQWkuyUtRQKbjdSDSWOM6PYlWog0huGu8ziIZKIZdh/sQsAMCmYxbeK0QY36XGCTPniRPFhSq2UKcY4vX5w3P9Ctg+nSgiCoP/ljqtznA1FFG86+gLbVZxrSAaK5dW5ECtkKG934n9rf1Sh0M0ap39nCdOJBXuRVEssjo9GHB5AXBtIBquWcUZSFLK0WN388BThDEpHidCrUaymRQnimnVuToAPJ1LsaHT6oTPL0IllyE7hesHUSSkJimh0yjgF4H2Ps4Vp8TQHqzuy+NmFdGYSVYrMD84L/C9g53w+nnQiuJbeJ441xKiMVcV3ouySRwJ0Umhw1KpSUpoVex8SzQccyYRygAAeCVJREFUKoUMc4OdpTYds0gcTWJhUjxOdAUrxTmXiSi2VefxdC7FjtbgPPHcNA1kMkHiaIgSgyAInCtOCSdcKZ7KSnGisXTxhCzo1Ar0OjzY3tAjdThEo8JKcSLpVBr1kAmAZcAFs80pdThEAE4eIue6QDQyF5ZlQiYAjd0OFmVEEJPicYLt04niQ6hl1RHTADw+VnuQtNp6OU+cKBpCc8VbOFecEkSHlbP+iKSgUshweVUOAODDejOcHp/EERGNHCvFiaSTpJKjJCsZAKvFKXZ08LAU0aikJilRnZcKAPi0kQdoI4VJ8ThhDm5U5eiZFCeKZYXpWqSoFXB7/TjRZZc6HBrn2oKnCDlPnCiyQnPFW3p5UpcSQ0dfqH061wuisTa7OANZKSo43D5sZGtEimOdVq4lRFIKFWlwnB/FinBSnIUaRCM2tyTQQn1PSx/cXhbgRQKT4nHCZA1VivNkFVEsk8mEz8xy4o0ISetkpbhW4kiIEkt+ehIEAP2DHlgHPVKHQzRqrO4jko5cJuDKaiMAYONRC2xOrisUn7iWEEmrmknxMfXYY4+hpKQEGo0GNTU12L59+zmvf/XVV1FZWQmNRoOpU6finXfeOeXrt912GwRBOOVj8eLF0fwRou7kwVuuC0QjVZadjIxkFVxeP/a39UkdTkJgUjxOhObBsFKcKPaFTudyrnh08MZj6EKV4vmsFCeKKLVCDoM+cGPPueIU76xODwZcXgBAHtunE0licp4eBelJcPv8+LDeLHU4RMMmiiI6+tgml0hKoaT4wXbuRUXbyy+/jJUrV+LnP/85du3ahWnTpmHRokUwm8+8hm/evBk33ngj7rjjDuzevRtLlizBkiVLcODAgVOuW7x4MTo6OsIfL7744lj8OFFz8rAU96SIRkomCJhdnA4A2NXcJ20wCYJJ8ThxcqY4by6IYh1P50YPbzyGJ5wUZ6sqoogrzAi2UO9hC3WKb6EkRmqSElqVQuJoiMYnQRCweHKgWnxHYy86+rm2UHzpsbsx6PFBEIBcJj+IJBEq0DhhscPp8UkcTWJ76KGHcNddd2H58uWorq7G2rVrodVq8dRTT53x+kceeQSLFy/Gfffdh6qqKvzqV7/CzJkz8eijj55ynVqthtFoDH+kp6efNQaXywWr1XrKR6wJvZ9hpTjR6EwvTIMAoMFiR6/DLXU4cY9J8Tgw6PbB5gxUb7BSnCj2VX3mdK4oihJHk1hi4cYjXvj9Yrh9OmeKE0VeYXpgLAErxSnetQc3q1jZRyStsuwUlGQmwyeK+MvHJ6QOh2hYWoP3HTk6NVQKbjUSScGgVyNdq4TPL+KoaUDqcBKW2+3Gzp07sXDhwvDnZDIZFi5ciC1btpzxMVu2bDnlegBYtGjRadevX78eOTk5qKiowD333IPu7u6zxrF69WqkpqaGPwoLC0fxU0WeKIqcKU4UIWlaFUqzkgEAe1v6pA0mAbAUIA6EWqdrlDLo1PwjI4p1FUYdZALQbXfDbHOFW+zS6IRuPFatWhX+3FBuPFauXHnK5xYtWoQ333zzlM+FbjzS09Nx+eWX4//9v/+HzMzMMz6ny+WCy+UK/zoWT+MCgGXABbfPD5nAuX5E0VCQEUiKt/UOwucXIZcJEkdENDKhSvE8blYRSe7yyhw8takBL25vxncum8BOcRQ3Qh2qCoKHBolo9F7Y1jzsx1Tn6bHpWDcOdVgxtSA1ClGRxWKBz+eDwWA45fMGgwH19fVnfExnZ+cZr+/s7Az/evHixbjuuutQWlqK48eP46c//SmuuuoqbNmyBXK5/LTnXLVq1Sn7XVarNaYS41anFw53oGOBkfuiRKM2oygNJyx27G7uw4JJ2RAE7kGNFI9vxgGTNZB8Meg1/MtOFAc0SjnKslMAcK54JJ3rxuOzNxKfNdQbj+eeew61tbX47W9/i48//hhXXXUVfL4ztxuL9dO4Ia3BjSmjXgOlnMs9UaTl6NRQyWVw+/w43sVKDIpfnawUJ4oZE7KTUZieBJfXj79taZI6HKIhC3Wo4tgmImlVGYOdC7kXFXduuOEGXHPNNZg6dSqWLFmCf/3rX/j000+xfv36M16vVquh1+tP+Yglodbp6VolklSnJ/WJaHgm56VCIRPQNeBCp9UpdThxjbvkcaCDG1VEcaf6My3UKbYN98Zj1apV6O/vD3+0tLSMbcBDFN6YYut0oqiQCQLy0gLvzfa19kscDdHItfezUpwoVgiCgC9MzAYAPL+1CYNuzoSl+NAaHCfDew8iaYXH+TEpHjVZWVmQy+UwmUynfN5kMsFoNJ7xMUajcVjXA0BZWRmysrJw7Nix0QctgVA3KmMq1wWiSNAo5ZiYEyjCq2O+YVSYFI8DofkbeVxEiOJGdV7gRuQQb0QiJlZuPGL9NG5IqIUhqzWIoifUInR/a5+0gRCNQugALtsaEsWG6jw9CjOS0Ovw4PXdrVKHQzQkJ9un896DSEqhpPihDitEUZQ4msSkUqkwa9Ys1NbWhj/n9/tRW1uLefPmnfEx8+bNO+V6AHj//ffPej0AtLa2oru7G7m5uZEJfIydzGfwHoMoUibnB8ZiHGhjYcZoMCkeBzpCLXC5iBDFDZ7OjTzeeAwPK8WJoi906GQfb0gojoWqOHLTeK9BFAtkgoDlF5UCAJ7Z1MikBsWFVrZPJ4oJ5TkpUMoF2Jze8GEViryVK1fiiSeewLPPPotDhw7hnnvugd1ux/LlywEAt9xyC1atWhW+/t5778W6devw4IMPor6+Hr/4xS+wY8cOrFixAgAwMDCA++67D1u3bkVjYyNqa2vx1a9+FeXl5Vi0aJEkP+NohTvf8h6DKGKqjHrIBMBsc6HL5pI6nLjFpHgcCLU0zOXNBVHcCLVPb7DY4XB7JY4mcfDGY+hOVoprJY6EKHGFDp0cbLfC4/NLHA3R8ImiiPbghhW7UhHFjutnFyBJKcdR8wA+beyVOpxx47HHHkNJSQk0Gg1qamqwffv2c17/6quvorKyEhqNBlOnTsU777wT/prH48GPf/xjTJ06FcnJycjL+//t3XlcVOX+B/DPzMCw7yCbCOIGKoJ7YG5JoZXLrdTMcsmsTEuvZWX3ulS3/FnZalezW2plZoupmWlI4p6KiCvixqayCMou28zz+2OY0VEQEIYzy+f9es2rmDlz+J7jcJ55nu95vo8fJkyYgMuXLxv6MCShvSGXM8WJpKW0kqN9KycAXM7PkMaOHYsPPvgA8+fPR0REBJKSkrB161Z4e3sDADIyMpCVlaXbPioqCt9//z1WrFiB8PBw/Pzzz9iwYQO6du0KAFAoFDh27BhGjBiBjh07YsqUKejZsyd2794NGxsbSY6xqS5rb7xlH4Oo2dgpFWjnpS2hzskZd4tJcROQrU2Ks6QhkcnwcrKBl5MNhABOZxdLHY7ZYMej4TKuatb1C3BnB4TIUNwdlLC1lqOiWo0zObzWk+kpKKtCeZXmhg5WpSIyHs621hgR7gcA+P5AusTRWIZ169Zh9uzZWLBgARITExEeHo6YmBjk5ubWuv2+ffswbtw4TJkyBUeOHMGoUaMwatQonDhxAgBQVlaGxMREzJs3D4mJiVi/fj1SUlIwYsSIljysFlF4vQrFFZobwf04mYNIcqG+mqR4chb7J4Y0Y8YMpKeno6KiAgcOHEDfvn11r8XHx2PVqlV6248ePRopKSmoqKjAiRMn8OCDD+pes7Ozw7Zt25Cbm4vKykqkpaVhxYoVurEuU5RdVDNTnH0MombV1U9TQp3rit89JsVNAMuNEJmmzr5cV9wQ2PGon1otkFmTFA90d5A4GiLzJZfJdIO/xy/yLl0yPdpZ4h4OSthaKySOhohuNv6eNgCALcezcbW0UuJozN+HH36IqVOnYvLkyejcuTOWL18Oe3t7fP3117Vu/8knn2Do0KGYM2cOQkND8fbbb6NHjx5YunQpAMDFxQWxsbEYM2YMOnXqhHvuuQdLly7F4cOHkZGR0ZKHZnDaWeIeDkrYK60kjoaIOBZFxiCLM8WJDCLUzxkyaCqEXmMf4a4wKW7kKqpVyCvRfLhZ0pDItHT203REeOcWtbTc4gpUVKuhkMt4QxWRgbWuWaKA64qTKbrM9cSJjFa31q7o4ueMSpUam5IuSR2OWausrMThw4cRHR2te04ulyM6Ohr79++v9T379+/X2x4AYmJi6tweAAoLCyGTyeDq6lrr6xUVFSgqKtJ7mIKL1zQ34/qzdDqRUdAmxU8xKU4SuXmJJs4UJ2pejjZWCPLUTIBiCfW7w6S4kcsprAAA2FjJ4WpvLXE0RNQYuo4Ik+LUwrSl0/1d7WCtYFNPZEjaAWDOFCdTpE1kBLjZSxwJEdXmsZ6tAQC/JDIpbkh5eXlQqVS3VYvy9vZGdnZ2re/Jzs5u1Pbl5eV47bXXMG7cODg7O9e6zaJFi+Di4qJ7BAQE3MXRtLyLNTPF/Vk6ncgohNaMRWVcLUNxeZXE0ZAlKrzOJZqIDKkLJ+I1CUfKjZz2rio/VzvIZDKJoyGixtDOFD+dXQSVWkgcDVmS9PxSAEAbdyY5iAytdc0A8OnsIlRUqySOhqhxtImM1pzdR2SURoT7wUouw/FLhTiTw7VhTVVVVRXGjBkDIQSWLVtW53Zz585FYWGh7pGZmdmCUd497Q25bTzY9yAyBm4OSvg4axKRKdlsO6jlaatRcYkmIsPoUrOueMbVMuSVVEgcjelhUtzIZRdqGhHtlxkiMh1BHg6wVypQXqVGal6J1OGQBcnkwBRRi3G1t4abvTWqVAKnszjoRKZFO1O8NWeKExklD0cbDOrUCgDwS+JFiaMxX56enlAoFMjJydF7PicnBz4+PrW+x8fHp0HbaxPi6enpiI2NrXOWOADY2NjA2dlZ72EKdH0P3pBLZDRCfZ0AcF1xkkZWzSQ/zhInMgwXO2v4udhCANiZckXqcEwOk+JGTrf+Btf5IzI5CrkMIT6ajgjLmVBLSufAFFGLkclkCGvtCoDripPp4UxxIuP3aA9/AMDvx7IgBKtPGYJSqUTPnj0RFxene06tViMuLg6RkZG1vicyMlJvewCIjY3V216bED979iy2b98ODw8PwxyAxLQzxbkUB5Hx0FYu5LriJIWsmkl+vi7sYxAZSqeanMNfKbkSR2J6mBQ3ctqZ4n5sRIhMEjsiJAXtwFQgk+JELSK8taZ01fGLBdIGQtRIN5LibC+IjNWgTq1gZ63AxWvXceIS+xSGMnv2bHz55ZdYvXo1kpOTMW3aNJSWlmLy5MkAgAkTJmDu3Lm67WfOnImtW7diyZIlOH36NBYuXIiEhATMmDEDgCYh/thjjyEhIQFr1qyBSqVCdnY2srOzUVlZKckxGoIQ4kb5dPY9iIyGdl3xU6xkRRLQzhT35UxxIoPp5K1Jiu86cwXVKrXE0ZgWK6kDoDu7XMByI0SmrLOvJlFyijPFqQVl5NfM1uDAFFGLCPPXXOuPXeRMcTIdReVVKLxeBQDw50xxIqNlp1RgcIgXthzPxpYTWQiruRGLmtfYsWNx5coVzJ8/H9nZ2YiIiMDWrVvh7e0NAMjIyIBcfmNeSVRUFL7//nv8+9//xhtvvIEOHTpgw4YN6Nq1KwDg0qVL2LRpEwAgIiJC73ft2LEDgwYNapHjMrQrxRWoqFZDLgP8XNmWEBkLbVI8JbsIKrWAQi6TOCKyJFk1a4qz8i2R4bR2t4e9UoHi8mocTr+GvsHmWZHIEJgUN3IsaUhk2rpoZ4pfLoIQAjIZOyJkWCUV1cgv1cw+4ZriRC2jW0359LO5JbheqYKdUiFtQEQNcKmmn+Fmbw1HG3YLiYzZsK6+2HI8G38cz8KrMZ3YpzCQGTNm6GZ63yo+Pv6250aPHo3Ro0fXun1QUJBFlLvXzhL3dbGD0orFKImMRZCHA2yt5SivUiMtvxTtvBylDoksyGXOFCcyOLlMho7eTkjKLMCOlCtMijcCv7EaMSGEbrCKJQ2JTFMnHyfIZUB+aSVyiyukDocsgHaWuJu9NZxtrSWOhsgyeDvbwMvJBiq1wKkszhY3Jp9//jmCgoJga2uLvn374uDBg3fc/qeffkJISAhsbW0RFhaGLVu26L0+adIkyGQyvcfQoUMNeQgGw9LpRKZjcEgr2FjJkZZfhtPZLIVLxoOl04mMk0IuQ4jPjUkaRC3pUgH7GUQtQVtCfcdpriveGEyKG7Gi69UorqgGAPizDBWRSbK1VujuyGVHhFpCxtVSAEAbDweJIyGyHDKZTLeuOEuoG49169Zh9uzZWLBgARITExEeHo6YmBjk5tbeYdy3bx/GjRuHKVOm4MiRIxg1ahRGjRqFEydO6G03dOhQZGVl6R5r165ticNpdhevaRIZrEhFZPwcbawwsKMXAOCP41kSR0N0A5PiRMZLW0I9OYtjUdRyqlVqXK4pn85+BpFhdfB2hFwGpOQU625GofoxKW7ELhZoOheejkqW4SQyYZ1rSqifvMxECRkeB6aIpBHm7woAOM6kuNH48MMPMXXqVEyePBmdO3fG8uXLYW9vj6+//rrW7T/55BMMHToUc+bMQWhoKN5++2306NEDS5cu1dvOxsYGPj4+uoebm1udMVRUVKCoqEjvYSy4TBORaRkW5gMA2HIiW+JIiG7Q9T24bBOR0ensq5lByKQ4taTsonKo1ALWChm8nVg+nciQ7JVW6BmoGY/gbPGGY1LciGkHqvxZaoTIpOnWFWdHhFpAek359EAmxYlaVDftTPFLTIobg8rKShw+fBjR0dG65+RyOaKjo7F///5a37N//3697QEgJibmtu3j4+PRqlUrdOrUCdOmTUN+fn6dcSxatAguLi66R0BAQBOOqnndmCnO9oLIFAwJ9Ya1QoZzuSU4m8MS6mQcMmuS4gHsexAZnRszxdlmUMvJvFqTz3C1g1wukzgaIvM3qFMrAEyKNwaT4kZMt544S6cTmbTOvppECcunU0vgTHEiaXT111zrz18pQUnN8jcknby8PKhUKnh7e+s97+3tjezs2mdZZmdn17v90KFD8c033yAuLg6LFy/Gzp07MWzYMKhUqlr3OXfuXBQWFuoemZmZTTyy5qO9iSrAnX0NIlPgbGuN/h1qSqhztjgZCW1bwr4HkfEJqUmKZxeV42pppcTRkKXQ3njLm6WIWsZ9IZqk+N7zeSivqn1cgvQxKW7EWNKQyDyE1pSsSssvQ3F5lcTRkLljCUMiaXg52cDPxRZCACc4W9xsPf744xgxYgTCwsIwatQobN68GYcOHUJ8fHyt29vY2MDZ2VnvYQyEEDfdROUgcTRE1FBDu2hKqG9PzpE4EiKgtKIaucUVAIC2HmxLiIyNo40VAmvGBVhCnVpKJvMZRC0qxMcJPs62KK9S42DqVanDMQlMihuxGyUN2YgQmTIPR02iBABOcrY4GVC1Sq2rMsLZGkQtr1trVwBcV9wYeHp6QqFQICdHP3GUk5MDHx+fWt/j4+PTqO0BIDg4GJ6enjh37lzTg25BeSWVKKtUQSbjTHEiUzIoRDNT/NjFQuQWl0scDVm61LxSAICbvTVc7K0ljoaIahPqoy2hzrEoahlcoomoZclkMgzsqOkj7DxzReJoTAOT4kbsUoF2TXEOVBGZOm1ZXc4eJEPKKixHtVpAaSWHj7Ot1OEQWZwwrituNJRKJXr27Im4uDjdc2q1GnFxcYiMjKz1PZGRkXrbA0BsbGyd2wPAxYsXkZ+fD19f3+YJvIVkXNUkMvxc7GBjpZA4GiJqqFZOtuhW09bEp3DQi6SVlq9pS9p6cpY4kbHSrit+iklxaiEXr3KmOFFLG9hJkxSPT+G64g3BpLgRu1E+nXdWEZk67eDVMc4eJAPSlsINcLODXC6TOBoiy6O91h+/WCBtIAQAmD17Nr788kusXr0aycnJmDZtGkpLSzF58mQAwIQJEzB37lzd9jNnzsTWrVuxZMkSnD59GgsXLkRCQgJmzJgBACgpKcGcOXPw999/Iy0tDXFxcRg5ciTat2+PmJgYSY7xbnENWCLTNbiTZt3Av5I56EXSSquZKR7EpDiR0ersV5MUZ9VCaiFcU5yo5fVr7wmFXIbzV0qRWTM2THVjUtxIFZdXofC6Zu1hf1feWUVk6sK0JXU5e5AMiEkOImmF1VQFScsvQ2FZlcTR0NixY/HBBx9g/vz5iIiIQFJSErZu3Qpvb28AQEZGBrKysnTbR0VF4fvvv8eKFSsQHh6On3/+GRs2bEDXrl0BAAqFAseOHcOIESPQsWNHTJkyBT179sTu3bthY2MjyTHeLW17oV1nkohMx5BQTVJ8z7k8VFarJY6GLNmFmqR4MJPiREYr1NcJAHD+SgnbDDK4ymo1soo0y7twpjhRy3Gxs0bPNm4AWEK9IYwiKf75558jKCgItra26Nu3Lw4ePHjH7X/66SeEhITA1tYWYWFh2LJli+61qqoqvPbaawgLC4ODgwP8/PwwYcIEXL582dCH0awya0qNuDso4WBjJXE0RNRU2kRJal4pisqZKCHD0M4UD/TgwBSRFFztlbqbUngTlHGYMWMG0tPTUVFRgQMHDqBv37661+Lj47Fq1Sq97UePHo2UlBRUVFTgxIkTePDBB3Wv2dnZYdu2bcjNzUVlZSXS0tKwYsUKXZLdlGjbizZMihOZnK5+LvB0tEFJRTUOpV2VOhyyYJwpTmT8/F3t4GpvjSqVQEp2sdThkJm7XHAdQgA2VnJ4OZrWTcNEpu5GCXUmxesjeVJ83bp1mD17NhYsWIDExESEh4cjJiYGubm1lwLbt28fxo0bhylTpuDIkSMYNWoURo0ahRMnTgAAysrKkJiYiHnz5iExMRHr169HSkoKRowY0ZKH1WTadf4424/IPLg7KHVVH7iuOBmKtu1gmSoi6eiWy7hUIG0gRHegXQc20J2JDCJTI5fLMLhm0Ouv0yyhTtJJq6k6wjXFiYyXTCZDeE3lwiQu8UQGlllTOr21mx1kMi7pR9SSBnbU9A/2nc9DRbVK4miMm+RJ8Q8//BBTp07F5MmT0blzZyxfvhz29vb4+uuva93+k08+wdChQzFnzhyEhobi7bffRo8ePbB06VIAgIuLC2JjYzFmzBh06tQJ99xzD5YuXYrDhw8jIyOjJQ+tSbSdiyDO3iAyGzfWmmVSnAzjwhVNkqOtJ9sOIqnwWk+mIIPl04lM2n0hmhLqO5gUJ4kUllXhamklACCIVaqIjFp4gCsA4GhmgaRxkPnjzVJE0uni5wwvJxuUVapwOO2a1OEYNUmT4pWVlTh8+DCio6N1z8nlckRHR2P//v21vmf//v162wNATExMndsDQGFhIWQyGVxdXWt9vaKiAkVFRXoPqaXXzN5ow84FkdnoWlNCnSV1yRDUaoFU3bp+jhJHQ2S5wvxdAQDHmBQnI1VSUY38mkQGy6cTmaZ7O3jCWiHDhbxS3fc/opaUWjNm5e1swyX/iIxcRIBmLIpJcTK09JrvJFzSj6jlyWQyDOhQU0Kd64rfkaRJ8by8PKhUqtvW4fP29kZ2dnat78nOzm7U9uXl5Xjttdcwbtw4ODs717rNokWL4OLionsEBATcxdE0r3TOFCcyO7rZg0yKkwFcKriOimo1rBUytHazkzocIovV1d8ZMpnmbzKvpELqcIhuo7351s3eGs621hJHQ0R3w8nWGn3augNgCXWSxoUrJQA4S5zIFHSrKZ9+7koJisurpA2GzJp2iSbmM4ikMUi3rjj7B3ciefl0Q6qqqsKYMWMghMCyZcvq3G7u3LkoLCzUPTIzM1swytqls6QhkdkJq5kpnp5fhsIydkSoeV246Y5cK4VZN+9ERs3J1hrBNeXieBMUGaMbS20wkUFkygZ30pRQ56AXSeFcriYp3r4VK1QRGTtPRxu0drODEOyfkGGl6fIZ7GcQSaF/B0/IZcCZnBJcLrgudThGS9JRc09PTygUCuTk5Og9n5OTAx8fn1rf4+Pj06DttQnx9PR0xMbG1jlLHABsbGzg7Oys95BSRbUKlws1H1o2IkTmw9VeiQB3zQzeE5fZEaHmpZ2tEcwkB5HktLMxuK44GSNtUrydFxMZRKZMOxPkwIWrKKusljgasjTapHgHJsWJTMKNdcXZPyHDUKkFMrimOJGkXO2ViKi53u9kCfU6SZoUVyqV6NmzJ+Li4nTPqdVqxMXFITIystb3REZG6m0PALGxsXrbaxPiZ8+exfbt2+Hh4WGYAzCQzKvXIQTgoFTAw0EpdThE1Iy6ca1ZMhBtkiOYSQ4iyWkrg/BaT8boQl7NTVRsL4hMWjsvR/i72qFSpcb+8/lSh0MW5sZMcSeJIyGihoiouWmX64qToWQXlaNSpVnSz9fFVupwiCzWIFaTqpfk9VVnz56NL7/8EqtXr0ZycjKmTZuG0tJSTJ48GQAwYcIEzJ07V7f9zJkzsXXrVixZsgSnT5/GwoULkZCQgBkzZgDQJMQfe+wxJCQkYM2aNVCpVMjOzkZ2djYqKyslOcbGyrh6owSuTCaTOBoiak5hunXFC6QNhMzOjSQH78glklq31tqkeIG0gRDV4sZNVGwviEyZTCbD4BDtuoGcCUItp6JahfSrmtmAHbx5gxWRKdDNFGf/hAwkvWZJvwA3ey7pRyQhbTWpvefyUaVSSxyNcZL8CjV27Fh88MEHmD9/PiIiIpCUlIStW7fC29sbAJCRkYGsrCzd9lFRUfj++++xYsUKhIeH4+eff8aGDRvQtWtXAMClS5ewadMmXLx4EREREfD19dU99u3bJ8kxNlZanqZzEeTJ9cSJzI129iDXcaLmdqMcLpMcRFLr7OcMuQzILa5ATlG51OEQ6QghdMttsL0gMn2DOtbMBDmTCyGExNGQpUjLK4NKLeBkY4VWTjZSh0NEDdDVX9M/ySosZ/+EDCI1XzvJj/kMIil19XOBh4MSJRXVOJx+TepwjJKV1AEAwIwZM3QzvW8VHx9/23OjR4/G6NGja90+KCjI5DuD6TWNSBt3DlQRmZuuNUnxzKvXkV9SAQ9HDiJQ05VVViOrUNOxDfbkbA0iqdkrrdDR2wmns4txNLMAD3TxkTokIgCaGzVKK1VQyGXsaxCZgaj2HlAq5Mi8eh0X8krRjssiUAvQlU73dmR1QyITwf4JGVp6zXrigR7sYxBJSS6XYUBHL/x65BLiU67gnmDTWlq6JUg+U5xud6Gm3EiwJxsRInPjYmeN9q00g1WJGQXSBkNmQztL3M3eGm4OSomjISIAiKgpUXg4g3fmkvE4XzNLPMDNDkordgWJTJ290gp92roDYAl1ajlnc4sBAO15EwaRSYlgCXUyoNSafEYQZ4oTSU5bQn3nGfYPasORECOkveu2XSt2MIjMUc82bgDAEibUbM7kaAamOng7SRwJEWn1CtIkKRLSeK0n43FjPXH2M4jMhXbQKz4lV+JIyFJox6y4njiRadGtK57J5fyo+WlvvmU/g0h6/Tt4QSYDkrOKuGRGLZgUNzIlFTdK4HKdPyLz1DNIkxRPZFKcmsmZHE3noyMHpoiMRu+aa/2xiwUor1JJHA2Rhi4pzopURGZDmxQ/kHoV1yvZ3pDh6cqncyIHkUkJb+0KADiaWQC12rSXHiXjUqVSI6OmfDrbBiLpuTso0a3mmr+T1aRuw6S4kblQc1eVp6MSrvYsgUtkjnoGahIlRy8WoLJaLXE0ZA7O1swU78iZ4kRGo427PbycbFClEjh2kbMxyDicu8KKVETmpp2XI/xd7VBZrcb+C3lSh0NmrqJapUuKh/g4SxwNETVGR29HOCgVKK6oRkrNGAJRc0jPL0W1WsBBqYCvi63U4RARgEEda6pJnWE1qVsxKW5ktKVG2rHUCJHZCvZ0gKu9NSqq1TiVVSR1OGQGztSs69ehFZPiRMZCJpPpZosfSrsqcTREGmeyeRMVkbmRyWQ3lVDnTBAyrHO5JahWCzjbWjHxQWRirBRy9Ahk/4Sa381LwcpkMomjISLgRjWpXWfyUFHNalI3Y1LcyHA9cSLzJ5PJuK44NZvSimpkXr0OgOXTiYxNb9264hx0IukVllUhu2Y9MbYXROZlUKdWADRJcSFYEpcM53SW5uaqUF9nJj6ITFDftpr+yYFU9k+o+eiW1eAkPyKjEd7aFa2cbFBSUY2/L/CafzMmxY3M+VzNOn9sRIjMm/buXK4rTk2l7Xx4Oirh4WgjcTREdDNdUjz9GtftI8lpy2T6u9rBydZa4miIqDlFtfOAUiFHxtUypOaVSh0OmbHkmkpnob4snU5kivq09QAAHEy9ypuoqNlwkh+R8ZHLZbi/szcA4M+T2RJHY1yYFDcyXOePyDJo1xVPSGdHhJrmTA5LpxMZqxAfJ826feXVumUOiKSiTYp38mF7QWRuHGys0Lutpn/BEupkSMnZ2qQ42xIiU9SttQuUCjmuFFcgLb9M6nDITGjzGe2ZzyAyKg908QEAxJ7K4USNmzApbkSqVGqk59fMFGcjQmTWwlu7QiGXIaeoApcLy6UOh0zY2Zo7clkKl8j46K/bx8ogJC2uJ05k3gZ11JRQ35GSK3EkZK6EEEiuKZ8e4sOZ4kSmyNZagYgAVwDAwdR8aYMhs6BWixuVb5nPIDIqkcEecLKxQm5xBY5eLJA6HKPBpLgRScsrRZVKwF6pgK+zrdThEJEB2SkV6OKnGUjguuLUFNoShh0584/IKPUK5LriZBxuzBTnYBWRORrUyQuAZp3Y65UqiaMhc3SluAJXSyshl/EGKyJT1qdmXfGDqRyLoqa7XHgd16tUsFbIEOhuL3U4RHQTpZUcg0I0N87+eSpH4miMB5PiRuRUTWIjxMcJcrlM4miIyNB6tOG64tQ0QgicvKxpO7r4uUgcDRHVpndQzUzxVCbFSTpCCN1yG0xkEJmn9q0c4e9qh8pqNfZfyJM6HDJDJ2vGrII8HWCnVEgcDRHdLV1SPI0zxanpUmqqUQV7OsJKwVQTkbF5gOuK34ZXKiOiLUMV6ssyVESWQLuuOGeK093KKdLM1lDIZQjhTHEioxTRRrNcxuXCclwquC51OGShcosrUFBWBbkMaOfFmeJE5kgmk2FgzWxxritOhnD8YiEAoJs/b8YlMmU9At2gkMuQefU6LrN/Qk2krV4Y6ssxKSJjNKiTF6wVMpy/UopzNUtwWjomxY3I6eyameJMihNZBG1S/FRWEUoqqiWOhkzRycuagal2Xg6wteZsDSJjZK+0Qtea5TL+Ps/ZGCSNE5c07UX7Vo5sL4jM2KCON5LiQgiJoyFzc6xmLcqw1q6SxkFETeNoY6Vbzu8Ql3iiJuIkPyLj5mRrjch2ngCAWJZQB8CkuFHR3lnVmXdWEVkEP1c7tHG3h0otWFaX7gpLpxOZhqj2mg7I3vMsZ0vSOF6TFO/K2X1EZi2qvSesFTJkXC1Dal6p1OGQGRFC4GjNTPHw1mxLiExdnyDtuuIci6KmuTFTnElxImOlLaH+x4ksiSMxDkyKG4mrpZXIKaoAAHTyYSNCZCmi2nkAAPZf4OxBajztTHHtXd5EZJzu1SbFz+Vx5h5J4sQlzWBVV95ERWTWHG2s0Lsm0fHX6VyJoyFzkl1UjivFFVDIZbwhl8gMaNcVP8CkODVBWWU1UvM1N+ExKU5kvIZ29YFCLsOxi4W4cIUl1JkUNxKna+6qCvSwh6ONlcTREFFLiaxJiu/j7EG6C9qZ4p2ZFCcyaj0D3WBjJUdOUQXOswNCEtCWTw/j7D4iszckVDMT5E+WR6RmdDRT0450aOUIOyWX4SAydX3aukMmA87lliC3qFzqcMhEpWQXQwjA09EGXk42UodDRHXwdLRB/w6ayRobki5LHI30mBQ3EqdqkuIhPiydTmRJIoM1SfGTl4tQWFYlcTRkSgrLqnDx2nUAQBdfJjmIjJmttUI3c2/3Wd4ERS3rSnEFsovKIZMBnTmDg8jsxXTRJMUT0q4iv6RC4mjIXGjXEw/neuJEZsHVXoluNcvq7GL/hO6Sdj1xTtQgMn7/6O4PANhw5JLFVzBkUtxIaGdvdGZig8iitHK2RTsvBwgB/J3KEurUcEk1A1Nt3O3hYm8tbTBEVK97a+7K3cNBJ2phJ2qW2gj2dIADK1IRmb3Wbvbo6u8MtQC2J3O2ODWPozV9D1YcITIf/Tt4AQB2n70icSRkqk5lafoZob6c5Edk7O7v7A17pQIZV8twJLNA6nAkxaS4kUiq+SBGtHGVNA4ianlR7ZgoocY7knENANCD7QaRSdCWqtp3Ph/lVSqJoyFLcuKiZrCqqz8TGUSWIqazDwBg64lsiSMhc1CtUuNIRgEAzZIwRGQetP2T3WfzoFZb9qxBujvHa/oZXfzYzyAydvZKKzzQWVNRauORSxJHIy0mxY1AQVkl0vLLAADhvOuWyOIM7Ki5Ozf+TK7Fly+hhtMOTHVvw4EpIlPQ2dcZPs62uF6lwt8XWBmEWo725tswJsWJLMbQrpqk+N5z+Sgu5xJN1DSnsopQVqmCs60VOnlzNiCRuejexg0OSgWullbqlvUkaqjyKpXuc9M9wFXaYIioQUbVlFD/7VgWqlRqiaORDpPiRkA7UNXW0wGu9kppgyGiFhfZzgPWChkyr17X3SBDdCdqtdC1Hd05U5zIJMhkMgwOaQUA+Ot0rsTRkKVQqwUO11QW4ew+IsvRvpUjgj0dUKlSIz6FZXGpaQ6mXgUA9Apyh1wukzgaImouSis5Itt5AADiU9g/ocY5lVWEKpWAh4MSrd3spA6HiBrg3vae8HRU4mpppUVXrGVS3AjoSqfzrioii+RgY4Vege4AgJ3siFADpOaXovB6FWys5AjxcZY6HCJqoCE3JcVZGYRawoW8EhSUadoLljUkshwymQwPdKkpoX6SJdSpaQ6laZPivLmKyNzcF6Ippbs9mWNR1DhJNdULIwJcIZPxhikiU2ClkOPhbn4AgPUWXEKdSXEjoE2Ks3Q6keUa2ElTQn3nGc7koPppOx9h/i5QWrEpJzIV/dp7wsZKjovXruNMTonU4ZAFOJyumSUeHuDK9oLIwsR00SQ64k/norxKJXE0ZKqEEEhI07QlfYLcJY6GiJrbkFDNTbtJmQXILS6XOBoyJZzkR2SaHuvZGgCw7UQ28ksqJI5GGhwZkZgQAke1jQjXhSWyWNp1xfedz0dZZbXE0ZCxS6hJcrB0OpFpsVMqcG97TwDAHyeyJI6GLIE2kdGLpdOJLE54a1f4ONuitFKFvecstzwiNc35K6XIL62E0kqOME7kIDI73s626Fbzt72DSzxRIxy9WAAAiOC4FJFJ6ervgvDWLqhUqfHz4YtShyMJJsUldi63BNdqShqG+jpJHQ4RSSTExwmt3exQUa3GrjMctKI7+/tCPgCgb1sPiSMhosZ6MMwXALDlOJPiZHjameIseUtkeeRyGYZ21ZRQ/+3oZYmjIVOlvaGiZxs32FgpJI6GiAwhOlRTWST2FJPi1DD5JRVIzy8DAHRr7SptMETUaE/0bQMA+P5gBtRqy1vaj0lxie2vSWz0DGQHg8iSyWQyPNBZM2j15ymu+0d1yyq8jtS8UshlQJ9gljAkMjXRnb1hrZDhTE4JzuUWSx0OmbErxRW4kFcKAOjBilREFmlEhGbNwD9P5bAaFd2V3Wc1y3sNqKlsRkTmR5sU3332Ckoq2FZQ/Q6kXgUAdPJ2goudtcTREFFjDQ/3g5OtFdLzyxB/xvJuiGJSXGLa2X6RwZztR2TptOv+xSXnokqlljgaMlb7z2vajTB/FzjbsvNBZGpc7Kx1JdQ3H+NscTIc7ey+rv7OcLVXShwNEUmhe4ArAj3sUVapQuypHKnDIRNTWa3W9T36d/CUOBoiMpRQXye09XRARbUacclsK6h+2rYhsh3zGUSmyF5phcd7BwAAvtqTKnE0LY9JcQmp1QJ/X9DcWcVGhIh6BbnD3UGJwutVOFhz1yXRrbSdj3vYbhCZrIe6aWbubUq6DCEsr1QVtYxdNbP77m3P2X1Elkomk2FkuKbN2XDkksTRkKlJzLiG0koVPByU6OzrLHU4RGQgMpkMD3fTLPHEm3apIbSVb+/hJD8ikzUxKggKuQx7z+Xj1OUiqcNpUUyKS+hMbjGullbCzlrB9TeICAq5DA901swW33yM6/5R7fadZ4URIlM3tKsP7KwVuJBXisSMAqnDITMkhMCes5qZ4gM4u4/Ioo3s7g8A2HU2DzlF5RJHQ6ZEWzq9fwdPyOUyiaMhIkN6uOam3Z0pV1BUXiVxNGTMcovLcS63BDIZcA+X9CMyWa3d7DGsq2Yp1xW7zkscTctiUlxC+85pEhu9gtygtOI/BREBI2pmcvx+LAsV1SqJoyFjc/5KCS4VXIe1QobeQex8EJkqRxsrDAvTdD5+PpwpcTRkjs7klCC3uAK21nL0DOJ64kSWrJ2XI3oFukGlFvj58EWpwyETEpesWWOS64kTmb9OPk7o0MoRlSo1tp7IljocMmLaqrehPlyiicjUPT+wHQBg09HLSM0rlTialsNMrIR2pNR0MDqwg0FEGn2DPeDtbIOi8mrEp1yROhwyMttr1oK8J9gDDjZWEkdDRE0xuqdm/abfjmbheiVvgqLmteuM5jtE37YesLFSSBwNEUltbM2agT8mZHLZDmqQ1LxSnM4uhpVchiEh3lKHQ0QtYFRNZZGfE3gDFdVtT00VES4FS2T6uvq7YEhIK6gFsPSvc1KH02I4oi6RovIq/F2z/kZ0Z3YwiEhDIZdhRLgfvtydio1JlxDTxUfqkMiIaGdrRIey3SAydX3buiPA3Q6ZV69jY9IlPN6njdQhkRn585Rmhs9Azu4jIgAPdfPFm7+dQnp+Gfafz0dUey6rQHf2xwnNusKR7TzgYm8tcTRE1BIe7dEaS/5MwcG0q7hwpQTBXo5Sh0RGRq0W+Ou0ZlxqcKdWEkdDRADw/YGMu37vE33b4KUhHRB3Ohcbki5h2qB2aN/K/K/9nCkukZ0pV1ClEmjn5YC2ng5Sh0NERmRkhObu3O2ncpFfUiFxNGQsrpVWIiFdU6ZqSCg7H0SmTi6XYcI9QQCAlXvTOHOPmk1ucTkS0q8B0KxfT0Rkr7TCqO6aZZpW7UuTNhgyCdryycO6+kocCRG1FB8XW90NlVxug2pzJLMAeSWVcLKxQp+2XNKPyByEB7giOrQVVGqBxVtPSx1Oi2BSXCLbkzUlcDlLnIhu1dXfBd1au6BSpWZHhHR2pORCLYBQX2e0drOXOhwiagZjegXAzlqBlJxi7D+fL3U4ZCa2ncyBEEBEgCv8XO2kDoeIjMSkqCAAQGxyDjLyy6QNhoxa5tUyHLtYCJkMuJ9jVkQWZUwv7XIbF1FRzSWeSJ82nzEopBWUVkwrEZmL14eFQCGXIfZUDg5cMP+xKV69JFBepdKVGrmfJXCJqBbj+2rK6K49mAG1mrMHCdh09DIADkwRmRMXe2s82lNTHeSLXRckjobMxR/HNSVvh3GWOBHdpH0rJwzo6AUhgNX706QOh4yY9sbsqHYe8HKykTgaImpJ0Z294e1sg7ySCmw+miV1OGRkYk/VTPJj9UIis9K+lRMe7625KWrBppOoUqkljsiwmBSXQFxyLorLq+HjbIvubdykDoeIjNDwcD842VghLb8Mu8/lSR0OSSy3uBy7z2o+B6Mi/CSOhoia0zP3BkMhl2HnmSs4knFN6nDIxOUUleNAqmapDZZOJ6JbPd0vCIDmxturpZXSBkNGSaUWuqS4dsYoEVkOa4UcEyKDAABf703lEk+kk5JdjHO5JbBWyDCI64kTmZ2XH+gEV3trnM4uxqq9aVKHY1BMikvgl0RNB+ORHv5QyGUSR0NExsheaYXHerUGACyPPy9xNCS1TUmXoVILRAS4ItjLUepwiKgZBXk64B/dNbPFP4k7K3E0ZOp+SsiESi3QO8gNgR4OUodDREZmYEcvdPFzRlmlCl/vSZU6HDJC+87n4VLBdTjbWiGmC2+uIrJET/RpAxsrOU5eLuIST6SjzWfcF9IKLnbWEkdDRM3N3UGJN4aFAgA+jD2DtLxSiSMyHCupA7A0V4orsPPMFQDAoz1bSxwNERmzZ/oH49v96dh/IR9HMq6xsoSFEkLg1yOXAACP9vCXOBoiqs33BzIatf0TNUtkaL14X3v8euQS4lOuYN/5PES182zO8MhCqNUC6xIyAQBje7epZ2siskQymQwvDemA5749jFX70vBM/7ZwtVdKHRYZkTV/a77TjOruD1trhcTREJEU3ByUGNMrAN/+nY6P484isp0HZDJO6rJk1So11idqxqUe68kqIkTm6rGerbH+yEX8feEqZv+YhB+fi4SVwvzmVZvfERm5nw9f1M32a8fZfkR0B/6udhhVM3vw8x2cLW6pEjOu4eTlIigVcjzcjaXTicxRoIcDxtckyt/cdArVZr5+ExnG3vN5yLx6HU62VngozFfqcIjISN0f6o0QHyeUVFRj6V/npA6HjMj5KyXYdiobAPDUPYESR0NEUnphcDsoFXIcTL3K2eKEnWeuIK+kAh4OSgzq5CV1OERkIHK5DB+MDoeTjRUSMwrwmZn2FZgUb0EV1Sqs3KspUfYkOxhE1ADPD2wHuQzYnpyDQ2lXpQ6HJPC/3Zp24x/d/eHmwJk8ROZq9v0d4WpvjZScYqzeny51OGSCVuy6AEDTXtgpObuPiGonl8sw90FNacTV+9PMujQiNc7/dl+AEEB0aCt08HaSOhwikpCvi52uutXibSlQq7m2uCVbtS8NgKaKiLUZzholohtau9njrVFdAACf/nUWO1JyJY6o+fEq1oI2HrmM3OIK+DjbYkQ4Z/sRUf3at3LUlUB95/dkCMGOiCXJyC/DtpOa2RpT+reVOBoiMiRXeyVejQkBALy/7TTOXymROCIyJUmZBdh9Ng8KuQxT+wdLHQ4RGbmBHb0wsKMXqlQCb28+xT4G4VLBdfxyWFMa9/mB7SSOhoiMwQuD2sFBqcDRzAL8XLOeNFmeE5cKdf2MSVFBUodDRC3gH91bY3zfNhACmLn2CM7mFEsdUrNiUryFVKnUWL5LU/54yr1tobTiqSeihvnn/R1gr1QgKbMAPx1mR8SSfPbXWagF0L+DJzpytgaR2Xu8dwD6d/BEeZUa/1yXhIpqldQhkYlY+tdZAMCoCH8EuNtLHA0RmYJ5D4fCWiFD3OlcbDp6WepwSGLvbT2NSpUa9wS7o1eQu9ThEJERaOVsi5nRHQAAi/84jcKyKokjIiks36nJZzzczZf9DCILMn94Z/QMdENReTUmrTyE7MJyqUNqNszMtpDv/k7HhSulcHdQ4vE+AVKHQ0QmpJWTLV4aoumI/GfzKeQUmU8jRHU7dblIdzf27Ps7ShwNEbUEuVyG9x7rBmdbKxy7WIj5G05y9h7Va++5PGxPzoVcpln/kYioIdq3csKL92n6GAs3nWQfw4IdybiGjUmXIZMB/36os9ThEJERmRTVFu28HJBfWol/bTjOvomFOXGpEL8fzwIAPDeA/QwiS2JjpcCXE3qhracDLhVcxxNf/m02/QUmxVvAtdJKfLxdM3vj5Qc6wsnWWuKIiMjUPHNvW3Rr7YKi8mq88tNRqLiek1kTQuA/v5+CEJq7cbu3cZM6JCJqIb4udvjsiR6Qy4B1CZlYvvOC1CGREauoVmHexhMAgAmRQWjn5ShxRERkSqYNaocufs64VlaF6WsSUaVSSx0StbDyKhVe++UYAOCR7q3R1d9F4oiIyJgoreRYMiYCCrkMm49l4dcjl6QOiVqIEAILN52EEMDwcD909nOWOiQiamHuDkp883Qf+Lva4UJeKcZ+sR/p+aVSh9VkTIobmBACc9cfR+H1KoT4OOHxmrWBiYgaw0ohx/uPhcPWWo7dZ/PwUewZqUMiA/ru73TsO58PpZVct8YwEVmOgR298MaDoQCAxVtP46s9qRJHRMbqw9gzuHClFJ6ONpj9AKuKEFHjWCvkWPpEDzjZWCEh/VrN4DdvvrUk729LwZmcEng6KvHGg+x3ENHtIgJcMbOmeuHc9ceRlFkgbUDUIn5KuIiE9Guws1awfSCyYAHu9lg79R74u9ohLb8Mjy7bh4S0q1KH1SRMihvYdwcysPVkNqwVmnKYCrlM6pCIyER18nHC/z3SDQCwdMc5rD2YIXFEZAgp2cV4Z0syAGDusBC08eCaTUSW6Jn+wXjpvvYAgLc3n8KiP5KhZpUQuknsqRx8UVNJ4O2RXeDMalREdBfaejpgyZhwyGTAmgMZ+JA331qM9YkXdTfeLX60GzwcbSSOiIiM1fTB7XFfSCtUVKvxzOoEnL9SInVIZEBncooxf5OmGtXM6A7wdbGTOCIiklIbD3v8+kIUOvs6I6+kEo+v+Btf7rpgspVsmRQ3oO2ncrBw00kAwKsxIejW2lXagIjI5I3q7o/nBgYDAN749Th+YGLcrGQVXseklQdRXqVG/w6emBgZJHVIRCShf97fEbPv18z+/WLnBUz4+iByzWQNJ2qahLSrmPnDEQDA5H5BGBbmK3FERGTKHujig7dGdgUAfPbXOby9+RRvxDJzO1JydWXTnxsQjCGh3hJHRETGTCGX4dNx3RHq64y8kgo8vuJvpGQXSx0WGcCV4go8/+1h3bjUs/2DpQ6JiIxAK2db/PR8JB7q5otqtcA7W5LxyLJ9OJ1dJHVojcakuIFsOZ6FF75PhEot8Eh3f0y5t63UIRGRmXh9aAgmRAZCCOD19cfxn82nUFnN9f9MXVpeKZ748gCyCsvRzssBnz7eHXJWFyGyaDKZDC8N6YCPx0bA1lqOPefyEP3hTnyzPw3VXPfVYu0+ewWTVh5CWaUK97b3xNxhoVKHRERm4Kl7AvHvhzTXk6/2pGLK6kPIK6mQOCoyhPWJFzF1dQKqVAIPdfPFa0NZFpeI6udoY4XvpvRBqK8zrhRX4JH/7sXvx7KkDouaUW5ROZ783wFcyCuFn4stPhobwXEpItJxsLHC0nHd8e4/wuBkY4WjmQV4+NM9eHvzKeSY0AQOJsWb2fVKFd7dkowX1iSislqNBzp7473HurEBIaJmI5PJsHB4F92aTv/bk4oRS/eY/HoeluyP41n4x3/3IjWvFP6udlj9dB+4OSilDouIjMSo7v7Y/OK96OrvjKLyaszfeBL3LdmJb/enofB6ldThUQspr1Lhw9gzmPj1QZRUVOOeYHd8OaEXlFbs0hFR83imfzA+eTwCSis5dqRcQcxHu7D2YIbJlkYkfQVllXj5x6OY/eNRVKsFRoT74aMxTHgQUcN5ONpg7dS+iAz2QGmlCtO/T8T07xNxueC61KFRE+07n4eHPtuDlJxieDvb4Pup98CTy2oQ0S1kMhme6NsGsbMHIqaLN6rVAl/tSUX/xTvwr1+P41yu8VcRMYoRlM8//xxBQUGwtbVF3759cfDgwTtu/9NPPyEkJAS2trYICwvDli1b9F4XQmD+/Pnw9fWFnZ0doqOjcfbsWUMeAgDg7wv5uP+jnVixS7O23zP3tsV/x/eAlcIoTjMRmRG5XIZ/3t8Ry5/sATd7a5zOLsZjy/djwtcHEZecY9Yzx82lzQCAw+nXMPHrg5i2JhHXyqrQrbULfp0ehdZuXEeciPS1b+WEjdPvxVsju8DDQYmMq2WYt/Eker+zHS+uPYKtJ7JwrbRS6jCNjrm0GfvP5yPm4134NO4s1AIY2ysAqyb3gZ1SYfDfTUSWZWSEPzbN6IdO3k7IL63E3PXHMfiDeHy9J9XsZ46bS5txq9zicnyy/Sz6v7cDvyRehEwGvHhfe3w8NoI3VhFRo7naK/HtlD54YVA7KOQy/H4sC4Pej8fc9cdw7GIBhLCMG6nMpc24XHAdL649gie+PIArxRXo5O2Edc9GIsjTweC/m4hMl4+LLb54qhdWP90HvQLdUKlSY82BDER/uAtDP96F/8afQ3JWkVEuyST5t99169Zh9uzZWLBgARITExEeHo6YmBjk5ubWuv2+ffswbtw4TJkyBUeOHMGoUaMwatQonDhxQrfNe++9h08//RTLly/HgQMH4ODggJiYGJSXG3YKv7VChovXrsPf1Q7/m9AL/364MxPiRGRQQ7v6Inb2QDzeOwAKuQy7zlzBlNUJ6P7Wn3hm9SGs3peGjPwyqcNsNubUZny7Pw2PLtuHnWeuwEouw4v3tcdPz0eilZOtQX8vEZkuhVyGCZFB2P3aYMx7uDM6eTuhslqN345exvPfJaLHf2Ix7JPdeP2XY/hqTyp2n72CC1dKUFJRLXXokjCnNuN6VTXS88vQyskGn47rjsWPdYOtNRPiRGQYIT7O2PzSvZj/cGe42Vsj42oZ3tp8Cn3e2Y4RS/fgP5tP4c+T2SgoM5+bscyqzahU4cCFfHy+4xweX7EfkYv+wkfbz6C4vBohPppkx8sPdOIMcSK6a1YKOV4dGoKN0/uhb1t3VKrUWHswEyOW7sU9i+Iw+8ck/Hz4Io5fLERxuflVtjKnNuOXwxfx29HLkMmA8X3b4NfpUUyIE1GDDezohZ+nRWHds/cgOtQb1goZTmcX472tKRj2yW50fzsWz6xOwIexZ7Dp6GWcvFyIq6WVkibLZULi27f69u2L3r17Y+nSpQAAtVqNgIAAvPjii3j99ddv237s2LEoLS3F5s2bdc/dc889iIiIwPLlyyGEgJ+fH15++WW88sorAIDCwkJ4e3tj1apVePzxx+uNqaioCC4uLigsLISzs3OjjmfL8SwM7OgFBxurRr2vpX1/IEPqECzeE33bNOn9/Dc0Dk39d2xO6fml+HZ/OjYkXUJeyY0BqifvaYP/jApr1L6ach00JHNqM3KLynHfkp14KMwX0wa1M7pOh6GvMY3922lsPNx/8zL14zX181kXIQROXCrC+iMXsedsHs7mltS5rb1SgVZONnBzUMLOWqF5KG/818ZKDoVcjgEdPBHV3rNRcbDNaJl+xrpDGXgwzBdOttaNel9Lk/o7anP8fTX1GKT+nm/pv99YYmgKY+pjlFVW45fES/j58EUczSzQe21q/7b410OdG7U/thmGbzNW70vDgk0n9Z7r0cYVE6OC8HA3PygkToZL/fdFZM6kaj8OXMjHmgMZ2Hoyu9bKhV5ONnhpSAc8dU9go/bLNsPwbUZ5lQpzfj6G5wcGo4ufS4Pf11KkaDPu9u+Isd5ZU65PLR2vKcXaFIZoMwrKKrH1RDZ+P56Fw+nXUFapqnU7K7kMHo5KeDnZwMXOGnbWVrBTKmBfM0b1YJgv+rR1b9Tvbuh1UNLMbWVlJQ4fPoy5c+fqnpPL5YiOjsb+/ftrfc/+/fsxe/ZsvediYmKwYcMGAEBqaiqys7MRHR2te93FxQV9+/bF/v37a21EKioqUFFxowxYYWEhAM1JbKx7Ax2gqihDkZFXFSsrNf7a/ububj5fN+O/oXFo6r9jc3KzBl4a0Boz7vXH6ewi7Dufj33n8tHH367RcWq3N6ayV+bWZtgC2Da9V01yQ2VUnyXA8NeYxh5vY+Ph/puXqR+vqZ/POwl0luGfAwPwz4EBuFJcjsT0ApzJKca5K8U4f6UUuUXlKKtUo6QCKCmu/7jkVe3RtZWyUTGwzWiZfsawTq4QlddRVGncazZK/R21Of6+mnoMUn/Pt/TfbywxNIWxfS8c2dkNIzu7IbvwOg6nX0NC+jUcTr+GLl5K9jNuYixtRid3BTysq9G9jSt6BLphYMdWCHDXLM9UWiL9OILUf19E5kyq9iPU0xr/eagd/v1AEI5kFODvC/lIyihAWn4p8ksrkVNRhqqyErYZNzGWNgMA3nmo3V2/19CkaDPu9jww1jtryuerpeM1pVibwhB/83IAD4a44sEQV1Sp1EjOKsKRjGs4n1uKC3mlSMsrQcH1alQCyLpeiqwrte/H30EgxKNx6euGthmSJsXz8vKgUqng7e2t97y3tzdOnz5d63uys7Nr3T47O1v3uva5ura51aJFi/Dmm2/e9nxAQEDDDoToLkyVOgBqFqbw7/hLE95bXFwMFxfjuFOUbYZ5MfTfDvffvEz9eC3tfDbFax8Dr93le9lm3I5tRsszhr8vqWOw9N8PGEcMTWEq8e/8192/l23G7Zq7zUgE8NVdvZOITJUxtx9TPgam3OV72Wbcjv0MwzHmv6NbMVbDMKVYm8KYj/PZj4Fn7/K99bUZxl3ju4XMnTtX724ttVqNq1evwsPDAzLZ7WWlioqKEBAQgMzMTKMq3dJQjF9aphy/KccOMP7GEEKguLgYfn5+Bv09pqixbYYpMvW/lbrwuEwLj8t0sM2omyW0GTczx8/3nfB4zRuP1zDYZtTNEtoMS/u7aiyenzvj+bkzczw/bDPqxjbDdPG4TAuPy3Q0tM2QNCnu6ekJhUKBnJwcvedzcnLg4+NT63t8fHzuuL32vzk5OfD19dXbJiIiotZ92tjYwMbGRu85V1fXeuN3dnY26Q8M45eWKcdvyrEDjL+hjOUuXC1TbzNMkan/rdSFx2VaeFymgW1GRK37tKQ242bm9vmuD4/XvPF4mx/bjIha92lJbYal/V01Fs/PnfH83Jm5nR+2GRG17pNthunjcZkWHpdpaEibIW+BOOqkVCrRs2dPxMXF6Z5Tq9WIi4tDZGRkre+JjIzU2x4AYmNjddu3bdsWPj4+etsUFRXhwIEDde6TiIiMH9sMIiJqKLYZRETUUGwziIioodhmEBGZNsnLp8+ePRsTJ05Er1690KdPH3z88ccoLS3F5MmTAQATJkyAv78/Fi1aBACYOXMmBg4ciCVLluChhx7CDz/8gISEBKxYsQIAIJPJMGvWLPznP/9Bhw4d0LZtW8ybNw9+fn4YNWqUVIdJRETNgG0GERE1FNsMIiJqKLYZRETUUGwziIhMl+RJ8bFjx+LKlSuYP38+srOzERERga1bt8Lb2xsAkJGRAbn8xoT2qKgofP/99/j3v/+NN954Ax06dMCGDRvQtWtX3TavvvoqSktL8eyzz6KgoAD33nsvtm7dCltb22aJ2cbGBgsWLLitRImpYPzSMuX4TTl2gPGbA1NsM0yRuX7WeFymhcdFTcU2o+VZ2uebx2veeLyWhW1Gy7D0z1l9eH7ujOfnznh+Wg7bjJZhrp9pHpdp4XGZH5kQQkgdBBERERERERERERERERERkSFIuqY4ERERERERERERERERERGRITEpTkREREREREREREREREREZotJcSIiIiIiIiIiIiIiIiIiMltMihMRERERERERERERERERkdliUrweaWlpmDJlCtq2bQs7Ozu0a9cOCxYsQGVl5R3fN2jQIMhkMr3H888/30JRA59//jmCgoJga2uLvn374uDBg3fc/qeffkJISAhsbW0RFhaGLVu2tFCk+hYtWoTevXvDyckJrVq1wqhRo5CSknLH96xateq2c21ra9tCEetbuHDhbbGEhITc8T3Gcu4BICgo6Lb4ZTIZpk+fXuv2Up/7Xbt2Yfjw4fDz84NMJsOGDRv0XhdCYP78+fD19YWdnR2io6Nx9uzZevfb2L8fQ8RfVVWF1157DWFhYXBwcICfnx8mTJiAy5cv33Gfd/MZJMtkqu1EXUy9/aiLqbcrdTG19qYupt4OETU3S/ns3k2bYy7+7//+DzKZDLNmzZI6FIO6dOkSnnzySXh4eMDOzg5hYWFISEiQOiyDUKlUmDdvnt6Yx9tvvw0hhNShkZmxlDbibrAfr89Q37HNRX3nZ9KkSbd9noYOHSpNsET14NiU8Y533IxjUxrG+m/FsanGYVK8HqdPn4ZarcYXX3yBkydP4qOPPsLy5cvxxhtv1PveqVOnIisrS/d47733WiBiYN26dZg9ezYWLFiAxMREhIeHIyYmBrm5ubVuv2/fPowbNw5TpkzBkSNHMGrUKIwaNQonTpxokXhvtnPnTkyfPh1///03YmNjUVVVhQceeAClpaV3fJ+zs7PeuU5PT2+hiG/XpUsXvVj27NlT57bGdO4B4NChQ3qxx8bGAgBGjx5d53ukPPelpaUIDw/H559/Xuvr7733Hj799FMsX74cBw4cgIODA2JiYlBeXl7nPhv792Oo+MvKypCYmIh58+YhMTER69evR0pKCkaMGFHvfhvzGSTLZMrtRF3Mof2oiym3K3UxtfamLqbeDhE1J0v67N5tm2PqDh06hC+++ALdunWTOhSDunbtGvr16wdra2v88ccfOHXqFJYsWQI3NzepQzOIxYsXY9myZVi6dCmSk5OxePFivPfee/jss8+kDo3MiCW1EXeL/fgbDPEd25zUd34AYOjQoXqfp7Vr17ZghEQNw7GpG4xxvONWHJvSMMZ/K45NNZKgRnvvvfdE27Zt77jNwIEDxcyZM1smoFv06dNHTJ8+XfezSqUSfn5+YtGiRbVuP2bMGPHQQw/pPde3b1/x3HPPGTTOhsjNzRUAxM6dO+vcZuXKlcLFxaXlgrqDBQsWiPDw8AZvb8znXgghZs6cKdq1ayfUanWtrxvTuQcgfv31V93ParVa+Pj4iPfff1/3XEFBgbCxsRFr166tcz+N/ftpLrfGX5uDBw8KACI9Pb3ObRr7GSTLZE7tRF1Mrf2oi7m1K3UxpfamLqbeDhE1lSV/dhvS5pi64uJi0aFDBxEbGytpX7clvPbaa+Lee++VOowW89BDD4mnn35a77lHHnlEjB8/XqKIyBxZchvREOzH1625vmObq9rGkiZOnChGjhwpSTxEjcGxKQ1TGO/g2JSGKfxbcWyqfpwpfhcKCwvh7u5e73Zr1qyBp6cnunbtirlz56KsrMzgsVVWVuLw4cOIjo7WPSeXyxEdHY39+/fX+p79+/frbQ8AMTExdW7fkgoLCwGg3vNdUlKCwMBABAQEYOTIkTh58mRLhFers2fPws/PD8HBwRg/fjwyMjLq3NaYz31lZSW+++47PP3005DJZHVuZ0zn/mapqanIzs7WO78uLi7o27dvnef3bv5+WlJhYSFkMhlcXV3vuF1jPoNkecytnaiLKbYfdTGXdqUupt7e1MUc2yGiulj6Z7ehbY4pmz59Oh566KHb2hhztGnTJvTq1QujR49Gq1at0L17d3z55ZdSh2UwUVFRiIuLw5kzZwAAR48exZ49ezBs2DCJIyNzYeltREOxH98wd/Md2xLFx8ejVatW6NSpE6ZNm4b8/HypQyLSw7EpfaYw3sGxKQ1T+Le6GcembsekeCOdO3cOn332GZ577rk7bvfEE0/gu+++w44dOzB37lx8++23ePLJJw0eX15eHlQqFby9vfWe9/b2RnZ2dq3vyc7ObtT2LUWtVmPWrFno168funbtWud2nTp1wtdff42NGzfiu+++g1qtRlRUFC5evNiC0Wr07dsXq1atwtatW7Fs2TKkpqaif//+KC4urnV7Yz33ALBhwwYUFBRg0qRJdW5jTOf+Vtpz2Jjzezd/Py2lvLwcr732GsaNGwdnZ+c6t2vsZ5Asjzm1E3UxxfajLubUrtTF1NubuphbO0R0J5b82W1om2PKfvjhByQmJmLRokVSh9IiLly4gGXLlqFDhw7Ytm0bpk2bhpdeegmrV6+WOjSDeP311/H4448jJCQE1tbW6N69O2bNmoXx48dLHRqZCUtuIxqK/fiGu5vv2JZm6NCh+OabbxAXF4fFixdj586dGDZsGFQqldShEelwbOoGUxjv4NiUhin8W92KY1O3s5I6AKm8/vrrWLx48R23SU5ORkhIiO7nS5cuYejQoRg9ejSmTp16x/c+++yzuv8PCwuDr68vhgwZgvPnz6Ndu3ZNC95CTJ8+HSdOnKh3HaXIyEhERkbqfo6KikJoaCi++OILvP3224YOU8/Nd9N369YNffv2RWBgIH788UdMmTKlRWNpqq+++grDhg2Dn59fndsY07k3Z1VVVRgzZgyEEFi2bNkdtzWnzyDR3TLF9qMulvA3zfaGiExZQ9scU5WZmYmZM2ciNjYWtra2UofTItRqNXr16oV3330XANC9e3ecOHECy5cvx8SJEyWOrvn9+OOPWLNmDb7//nt06dIFSUlJmDVrFvz8/MzyeImMkSV856eW8/jjj+v+PywsDN26dUO7du0QHx+PIUOGSBgZkWXh2JRp4diU5bDYpPjLL798x7s+ACA4OFj3/5cvX8bgwYMRFRWFFStWNPr39e3bF4Bmprkhk+Kenp5QKBTIycnRez4nJwc+Pj61vsfHx6dR27eEGTNmYPPmzdi1axdat27dqPdq724/d+6cgaJrOFdXV3Ts2LHOWIzx3ANAeno6tm/fjvXr1zfqfcZ07rXnMCcnB76+vrrnc3JyEBERUet77ubvx9C0CfH09HT89ddfd5wlXpv6PoNkecylnaiLubQfdTHVdqUu5tDe1MVc2iGihrDUz25T2hxTcfjwYeTm5qJHjx6651QqFXbt2oWlS5eioqICCoVCwgibn6+vLzp37qz3XGhoKH755ReJIjKsOXPm6GaLA5oESnp6OhYtWsSkODULS20jmoL9+LrdzXdsSxccHAxPT0+cO3eOSXEyGhybqpspjHdwbErDFP6tODZ1O4stn+7l5YWQkJA7PpRKJQDNDPFBgwahZ8+eWLlyJeTyxp+2pKQkAND74BmCUqlEz549ERcXp3tOrVYjLi5O7y6Wm0VGRuptDwCxsbF1bm9IQgjMmDEDv/76K/766y+0bdu20ftQqVQ4fvy4wc91Q5SUlOD8+fN1xmJM5/5mK1euRKtWrfDQQw816n3GdO7btm0LHx8fvfNbVFSEAwcO1Hl+7+bvx5C0CfGzZ89i+/bt8PDwaPQ+6vsMkuUx9XaiLubWftTFVNuVuphDe1MXc2iHiBrK0j67zdHmmIohQ4bg+PHjSEpK0j169eqF8ePHIykpyewS4gDQr18/pKSk6D135swZBAYGShSRYZWVld02xqFQKKBWqyWKiMyNpbURzYH9+LrdzXdsS3fx4kXk5+fz80RGhWNTdTOF8Q6OTWmYwr8Vx6ZqIeiOLl68KNq3by+GDBkiLl68KLKysnSPm7fp1KmTOHDggBBCiHPnzom33npLJCQkiNTUVLFx40YRHBwsBgwY0CIx//DDD8LGxkasWrVKnDp1Sjz77LPC1dVVZGdnCyGEeOqpp8Trr7+u237v3r3CyspKfPDBByI5OVksWLBAWFtbi+PHj7dIvDebNm2acHFxEfHx8XrnuqysTLfNrfG/+eabYtu2beL8+fPi8OHD4vHHHxe2trbi5MmTLR7/yy+/LOLj40VqaqrYu3eviI6OFp6eniI3N7fW2I3p3GupVCrRpk0b8dprr932mrGd++LiYnHkyBFx5MgRAUB8+OGH4siRIyI9PV0IIcT//d//CVdXV7Fx40Zx7NgxMXLkSNG2bVtx/fp13T7uu+8+8dlnn+l+ru/vp6Xir6ysFCNGjBCtW7cWSUlJen8PFRUVdcZf32eQSAjTbifqYurtR13MoV2piym1N3Ux9XaIqDlZ0me3IW2OORs4cKCYOXOm1GEYzMGDB4WVlZV45513xNmzZ8WaNWuEvb29+O6776QOzSAmTpwo/P39xebNm0VqaqpYv3698PT0FK+++qrUoZEZsaQ24m6wH6+vOb5jm7M7nZ/i4mLxyiuviP3794vU1FSxfft20aNHD9GhQwdRXl4udehEejg2pWGs4x0349iUhrH+W3FsqnGYFK/HypUrBYBaH1qpqakCgNixY4cQQoiMjAwxYMAA4e7uLmxsbET79u3FnDlzRGFhYYvF/dlnn4k2bdoIpVIp+vTpI/7++2/dawMHDhQTJ07U2/7HH38UHTt2FEqlUnTp0kX8/vvvLRbrzeo61ytXrtRtc2v8s2bN0h2rt7e3ePDBB0ViYmLLBy+EGDt2rPD19RVKpVL4+/uLsWPHinPnzuleN+Zzr7Vt2zYBQKSkpNz2mrGd+x07dtT6edHGqFarxbx584S3t7ewsbERQ4YMue24AgMDxYIFC/Seu9PfT0vFr72u1PbQXmtqi7++zyCRlqm2E3Ux9fajLubQrtTFlNqbuph6O0TU3Czls9uQNsecmXtSXAghfvvtN9G1a1dhY2MjQkJCxIoVK6QOyWCKiorEzJkzRZs2bYStra0IDg4W//rXv/RuxCVqDpbSRtwN9uP1Ncd3bHN2p/NTVlYmHnjgAeHl5SWsra1FYGCgmDp1qlkkMcg8cWzKeMc7bsaxKQ1j/bfi2FTjyIQQ4u7mmBMRERERERERERERERERERk3i11TnIiIiIiIiIiIiIiIiIiIzB+T4kREREREREREREREREREZLaYFCciIiIiIiIiIiIiIiIiIrPFpDgREREREREREREREREREZktJsWJiIiIiIiIiIiIiIiIiMhsMSlORERERERERERERERERERmi0lxIiIiIiIiIiIiIiIiIiIyW0yKExERERERERERERERERGR2WJSnEzepEmTMGrUKKnDMFsLFy5ERESE1GEQETVYUFAQPv744wZvv2rVKri6uhosHmq4tLQ0yGQyJCUlSR0KEZHBWcr37IZc2+Pj4yGTyVBQUNBicRERGZJMJsOGDRsM/nsa2/e5G81xjW7J6/ygQYMwa9Ysg/8eIiJzI4TAs88+C3d3d47NkNliUpyMmkwmu+Nj4cKF+OSTT7Bq1SqpQ5VcUwbVFi1aBIVCgffff795gyIiqsWVK1cwbdo0tGnTBjY2NvDx8UFMTAz27t3bLPs/dOgQnn322WbZl7EyRCKlrpsDWmKgjYjI1GRnZ2PmzJlo3749bG1t4e3tjX79+mHZsmUoKytr0D5eeeUVxMXFGTjS5tfYZENAQACysrLQtWtXwwVFRGRgkyZNqnVc6ty5c7Vun5WVhWHDhrVwlIYRFRWFrKwsuLi4SB1Kg6xfvx5vv/221GEQETXIze2LUqlE+/bt8dZbb6G6urpJ+72bm5G2bt2KVatWYfPmzfz+TmbLSuoAiO4kKytL9//r1q3D/PnzkZKSonvO0dERjo6OUoRmVr7++mu8+uqr+PrrrzFnzhypwyEiM/foo4+isrISq1evRnBwMHJychAXF4f8/Pxm2b+Xl1ez7IeIiKg2Fy5cQL9+/eDq6op3330XYWFhsLGxwfHjx7FixQr4+/tjxIgR9e7HUvoyCoUCPj4+UodBRNRkQ4cOxcqVK/Weu7XvUVlZCaVSaVbXvfqOR6VSQSaTQS43jrlX7u7uUodARNQo2valoqICW7ZswfTp02FtbY25c+e2aBznz5+Hr68voqKi6txG284RmSrj+LZCVAcfHx/dw8XFBTKZTO85R0fH28qnDxo0CC+++CJmzZoFNzc3eHt748svv0RpaSkmT54MJycntG/fHn/88Yfe7zpx4gSGDRsGR0dHeHt746mnnkJeXl6D4vz5558RFhYGOzs7eHh4IDo6GqWlpQBulHd/88034eXlBWdnZzz//POorKzUvV+tVmPRokVo27Yt7OzsEB4ejp9//ln3uvbOrri4OPTq1Qv29vaIiorS3SCwatUqvPnmmzh69KjuzrKGzp7fuXMnrl+/jrfeegtFRUXYt2/fHbdXq9V466230Lp1a9jY2CAiIgJbt27Vva4tj7h+/XoMHjwY9vb2CA8Px/79+/X2s2fPHvTv3x92dnYICAjASy+9pDtnRGS+CgoKsHv3bixevBiDBw9GYGAg+vTpg7lz5+oSCBkZGRg5ciQcHR3h7OyMMWPGICcnR28/v/32G3r37g1bW1t4enriH//4h+61W2c2f/jhhwgLC4ODgwMCAgLwwgsvoKSk5K6PYePGjejRowdsbW0RHByMN998U3cH7xNPPIGxY8fqbV9VVQVPT0988803AKS75t/pPMTHx2Py5MkoLCzUq8YyaNAgpKen45///Kfuea36ruNBQUF499138fTTT8PJyQlt2rTBihUr9GI6ePAgunfvDltbW/Tq1QtHjhzRe12lUmHKlCm6c9WpUyd88sknetto29kPPvgAvr6+8PDwwPTp01FVVaXbpqKiAq+99hoCAgJgY2OD9u3b46uvvtK93pTvAERkeV544QVYWVkhISEBY8aMQWhoKIKDgzFy5Ej8/vvvGD58OID627Nbq3405HqWlZWFhx56CHZ2dmjbti2+//77RlX0qC+m2pammjVrFgYNGqR7fefOnfjkk0907UJaWhquXbuG8ePHw8vLC3Z2dujQoYMucVRb+fQtW7agY8eOsLOzw+DBg5GWlnZbrOwvEJGx0Va5uvkxZMgQzJgxA7NmzYKnpydiYmIA3F4+PTMzE2PGjIGrqyvc3d0xcuRIvWtfQ9qA3NxcDB8+XNcGrFmzRi8+IQQWLlyoq8jl5+eHl156qUHHdqfvy7fONtRWmNq0aRM6d+4MGxsbZGRk1Pud+1ZNuc7/97//RYcOHXTVWh577DHdazdXNNHGfutj0qRJuu3v1L8jImoJ2vYlMDAQ06ZNQ3R0NDZt2oRr165hwoQJcHNzg729PYYNG4azZ8/q3peeno7hw4fDzc0NDg4O6NKlC7Zs2YK0tDQMHjwYAODm5nbbda82kyZNwosvvoiMjAzIZDIEBQUB0FxTa2vn6htHKS0txYQJE+Do6AhfX18sWbLktopTtS014urqqjfG1RztZ13tkxAC7du3xwcffKAXQ1JS0h2rwZBpY1KczNLq1avh6emJgwcP4sUXX8S0adMwevRoREVFITExEQ888ACeeuopXWnDgoIC3HfffejevTsSEhKwdetW5OTkYMyYMfX+rqysLIwbNw5PP/00kpOTER8fj0ceeQRCCN02cXFxutfWrl2L9evX480339S9vmjRInzzzTdYvnw5Tp48iX/+85948sknsXPnTr3f9a9//QtLlixBQkICrKys8PTTTwMAxo4di5dffhldunRBVlYWsrKybkvK1OWrr77CuHHjYG1tjXHjxt2xwwIAn3zyCZYsWYIPPvgAx44dQ0xMDEaMGKHXIGtjfeWVV5CUlISOHTti3Lhxuk7F+fPnMXToUDz66KM4duwY1q1bhz179mDGjBkNipmITJd2VtyGDRtQUVFx2+tqtRojR47E1atXsXPnTsTGxuLChQt617Tff/8d//jHP/Dggw/iyJEjiIuLQ58+fer8nXK5HJ9++ilOnjyJ1atX46+//sKrr756V/Hv3r0bEyZMwMyZM3Hq1Cl88cUXWLVqFd555x0AwPjx4/Hbb7/pJd23bduGsrIyXeJeqmv+nc5DVFQUPv74Yzg7O+v2+corr2D9+vVo3bo13nrrLd3zQMOv40uWLNElu1944QVMmzZNl9wvKSnBww8/jM6dO+Pw4cNYuHAhXnnlFb33q9VqtG7dGj/99BNOnTqF+fPn44033sCPP/6ot92OHTtw/vx57NixA6tXr8aqVav0OlETJkzA2rVr8emnnyI5ORlffPGFbnZmU74DEJHlyc/Px59//onp06fDwcGh1m1kMlmD2rPaNOR6dvnyZcTHx+OXX37BihUrkJub26DY7zamm33yySeIjIzE1KlTde1CQEAA5s2bh1OnTuGPP/5AcnIyli1bBk9Pz1r3kZmZiUceeQTDhw9HUlISnnnmGbz++ut627C/QESmZPXq1VAqldi7dy+WL19+2+tVVVWIiYmBk5MTdu/ejb1798LR0RFDhw7VmzBRXxswadIkZGZmYseOHfj555/x3//+V68N+OWXX/DRRx/hiy++wNmzZ7FhwwaEhYU16Bju9H25NmVlZVi8eDH+97//4eTJk2jVqlWj9tGU63xCQgJeeuklvPXWW0hJScHWrVsxYMCAWrfVln7XPv766y/Y2trqtq+vf0dEJAU7OztUVlZi0qRJSEhIwKZNm7B//34IIfDggw/qEr7Tp09HRUUFdu3ahePHj2Px4sVwdHREQEAAfvnlFwBASkoKsrKybptgcKtPPvlENxEuKysLhw4d0r12azvXkHGUOXPmYOfOndi4cSP+/PNPxMfHIzExsVHnobnaz7raJ5lMhqeffvq2KjArV67EgAED0L59+0bFSyZCEJmIlStXChcXl9uenzhxohg5cqTu54EDB4p7771X93N1dbVwcHAQTz31lO65rKwsAUDs379fCCHE22+/LR544AG9/WZmZgoAIiUl5Y5xHT58WAAQaWlptb4+ceJE4e7uLkpLS3XPLVu2TDg6OgqVSiXKy8uFvb292Ldvn977pkyZIsaNGyeEEGLHjh0CgNi+fbvu9d9//10AENevXxdCCLFgwQIRHh5+x1hvVVhYKOzs7ERSUpIQQogjR44IR0dHUVxcrNvm1v36+fmJd955R28/vXv3Fi+88IIQQojU1FQBQPzvf//TvX7y5EkBQCQnJ+uO7dlnn9Xbx+7du4VcLtcdDxGZr59//lm4ubkJW1tbERUVJebOnSuOHj0qhBDizz//FAqFQmRkZOi2115DDh48KIQQIjIyUowfP77O/QcGBoqPPvqoztd/+ukn4eHhofu5rvalNkOGDBHvvvuu3nPffvut8PX1FUIIUVVVJTw9PcU333yje33cuHFi7NixQggh6TX/Vg09D7Wdz4ZcxwMDA8WTTz6pe12tVotWrVqJZcuWCSGE+OKLL4SHh4fedX/ZsmUCgDhy5EidcU+fPl08+uijup8nTpwoAgMDRXV1te650aNH6855SkqKACBiY2Nr3V9TvgMQkeX5+++/BQCxfv16vec9PDyEg4ODcHBwEK+++mqD2rNbr+X1Xc+Sk5MFAHHo0CHd62fPnhUA7tjuaTUkplv7VkIIMXPmTDFw4EDdzwMHDhQzZ87U22b48OFi8uTJtf5ebf9Ae22fO3eu6Ny5s942r732mgAgrl27JoRgf4GIjM/EiROFQqHQXesdHBzEY489JgYOHCi6d+9+2/YAxK+//iqE0PQXOnXqJNRqte71iooKYWdnJ7Zt26bbf0O+02qv10LcaBe0bcCSJUtEx44dRWVlZaOOrb7vy9r+ifYavXLlSgFAN5Z0N/toynX+l19+Ec7OzqKoqKjW12trp4QQIi8vTwQHB+vGr4Sov39HRGRoN3//VqvVIjY2VtjY2IhRo0YJAGLv3r26bfPy8oSdnZ348ccfhRBChIWFiYULF9a631uvuw3x0UcficDAQL3namvn6htHKS4uFkqlUhenEELk5+cLOzs7vevzzW2llouLi1i5cqUQonnbz7rap0uXLgmFQiEOHDgghBCisrJSeHp6ilWrVtVxlsjUcaY4maVu3brp/l+hUMDDw0Pv7lhvb28A0N1Re/ToUezYsUM3g9HR0REhISEANHev3kl4eDiGDBmCsLAwjB49Gl9++SWuXbt22zb29va6nyMjI1FSUoLMzEycO3cOZWVluP/++/V+/zfffHPb7775uHx9ffWO4W6sXbsW7dq1Q3h4OAAgIiICgYGBWLduXa3bFxUV4fLly+jXr5/e8/369UNycnKDYz169ChWrVqld7wxMTFQq9VITU296+MhItPw6KOP4vLly9i0aROGDh2K+Ph49OjRA6tWrUJycjICAgIQEBCg275z585wdXXVXWeSkpIwZMiQBv++7du3Y8iQIfD394eTkxOeeuop5Ofn66qFNMbRo0fx1ltv6V2/tLPlysrKYGVlhTFjxuhKGZaWlmLjxo0YP348AEh6zW/u89CQ6/jNx6BdAkV7DMnJyejWrRtsbW1120RGRt72uz7//HP07NkTXl5ecHR0xIoVK5CRkaG3TZcuXaBQKHQ/+/r66n5PUlISFAoFBg4cWOex3O13ACIirYMHDyIpKQldunRBRUVFg9qz2tzpepaSkgIrKyv06NFD93r79u3h5ubWoBjvNqaGmDZtGn744QdERETg1VdfveOSTMnJyejbt6/ec7de/9lfICJjNHjwYCQlJeken376KQCgZ8+ed3zf0aNHce7cOTg5Oemuae7u7igvL9f7vnmnNiA5ORlWVlZ6vyskJASurq66n0ePHo3r168jODgYU6dOxa+//tqgMuD1fV+ujVKp1Puu39h9NOU6f//99yMwMBDBwcF46qmnsGbNmnr7NFVVVXj00UcRGBioN1uyvv4dEVFL2Lx5MxwdHWFra4thw4Zh7NixmDRpEqysrPS+N3t4eKBTp0667+4vvfQS/vOf/6Bfv35YsGABjh07ZpD4bm3n6htHOX/+PCorK/Vid3d3R6dOnRr1e5uj/ayvffLz88NDDz2Er7/+GoBmucaKigqMHj26UbGS6bCSOgAiQ7C2ttb7WSaT6T2nXRNVrVYD0JRwHT58OBYvXnzbvrSJiLooFArExsZi3759+PPPP/HZZ5/hX//6Fw4cOIC2bdvWG6u2xO7vv/8Of39/vddsbGzqPK5bj+FufPXVVzh58iSsrG5cCtRqNb7++mtMmTLlrvcL3DnWkpISPPfcc7WubdWmTZsm/V4iMg22tra4//77cf/992PevHl45plnsGDBArz88sv1vtfOzq7BvyctLQ0PP/wwpk2bhnfeeQfu7u7Ys2cPpkyZgsrKSr0blhqipKQEb775Jh555JFajwnQlFAfOHAgcnNzERsbCzs7OwwdOlT3fqDlr/mGOA8NuY7X1h435hh++OEHvPLKK1iyZAkiIyPh5OSE999/HwcOHNDb7k6/p77PS1O+AxCR5Wnfvj1kMpluKQit4OBgAI1ro2rT1OtmU8nlcr1loADorcdXl2HDhiE9PR1btmxBbGwshgwZgunTp9+2Pl9Dsb9ARMbIwcGh1lKqdS2noVVSUoKePXvetgY4AHh5een+v6ltQEBAAFJSUrB9+3bExsbihRdewPvvv4+dO3fetu+b3U3bZWdnp+uj3M0+mnKdd3JyQmJiIuLj4/Hnn39i/vz5WLhwIQ4dOqR3k8DNpk2bhszMTBw8eFBvDKwh/TsiIkMbPHgwli1bBqVSCT8/P1hZWWHTpk31vu+ZZ55BTEwMfv/9d/z5559YtGgRlixZghdffLFZ47u1natvHKWha3HLZLI79j2ao/1sSPv0zDPP4KmnnsJHH32ElStXYuzYsY0eJyPTwaQ4EYAePXrgl19+QVBQkN6X44aSyWTo168f+vXrh/nz5yMwMBC//vorZs+eDUBzV9P169d1F+G///5bt76Hu7s7bGxskJGR0ai7cm+lVCqhUqkavP3x48eRkJCA+Ph4uLu7656/evUqBg0ahNOnT+vu8NJydnaGn58f9u7dqxfr3r1777ie76169OiBU6dOcV0OItLp3LkzNmzYgNDQUGRmZiIzM1M3k+3UqVMoKChA586dAWhmH8fFxWHy5Mn17vfw4cNQq9VYsmQJ5HJNgZxb16NujB49eiAlJeWO16+oqCgEBARg3bp1+OOPPzB69GjdF/TOnTtLcs1vyHmoa5+1Pd8c1/HQ0FB8++23KC8v1w04/f3333rb7N27F1FRUXjhhRd0zzV29nZYWBjUajV27tyJ6Ojo215v6ncAIrIsHh4euP/++7F06VK8+OKLdSZCGtKeNVanTp1QXV2NI0eO6GZrnDt37rYqVXVpSExeXl44ceKE3vuSkpL0Bprqai+8vLwwceJETJw4Ef3798ecOXNqTYqHhobeNsh36/Wf/QUiMic9evTAunXr0KpVKzg7O9/VPkJCQlBdXY3Dhw+jd+/eADQVRAoKCvS2s7Ozw/DhwzF8+HBMnz4dISEhOH78uF6VkVvV9325IRq7j6Ze562srBAdHY3o6GgsWLAArq6u+Ouvv2pNbn/44Yf48ccfsW/fPnh4eNwWR339OyIiQ6vtpqvQ0FBUV1fjwIEDiIqKAgDk5+cjJSVFrz8REBCA559/Hs8//zzmzp2LL7/8Ei+++CKUSiUANGrsqKHqG0dp164drK2tceDAAd2NTteuXcOZM2f0xsK8vLyQlZWl+/ns2bN6VTqao/1sSPv04IMPwsHBAcuWLcPWrVuxa9euu/pdZBpYPp0IwPTp03H16lWMGzcOhw4dwvnz57Ft2zZMnjy53objwIEDePfdd5GQkICMjAysX78eV65cQWhoqG6byspKTJkyBadOncKWLVuwYMECzJgxA3K5HE5OTnjllVfwz3/+E6tXr8b58+eRmJiIzz77DKtXr27wMQQFBSE1NRVJSUnIy8tDRUXFHbf/6quv0KdPHwwYMABdu3bVPQYMGIDevXvjq6++qvV9c+bMweLFi7Fu3TqkpKTg9ddfR1JSEmbOnNngWF977TXs27cPM2bMQFJSEs6ePYuNGzdixowZDd4HEZmm/Px83Hffffjuu+9w7NgxpKam4qeffsJ7772HkSNHIjo6GmFhYRg/fjwSExNx8OBBTJgwAQMHDkSvXr0AAAsWLMDatWuxYMECJCcn4/jx47XenQpoZvRVVVXhs88+w4ULF/Dtt99i+fLldx3//Pnz8c033+DNN9/EyZMnkZycjB9++AH//ve/9bZ74oknsHz5csTGxupKpwOQ7JrfkPMQFBSEkpISxMXFIS8vT9cRCQoKwq5du3Dp0iXk5eUBaJ7r+BNPPAGZTIapU6fq2sdbkycdOnRAQkICtm3bhjNnzmDevHk4dOhQg3+HNv6JEyfi6aefxoYNG5Camor4+HjdTQFN+Q5ARJbpv//9L6qrq9GrVy+sW7cOycnJSElJwXfffYfTp09DoVA0qD1rrJCQEERHR+PZZ5/FwYMHceTIETz77LO3zdarS0Niuu+++5CQkIBvvvkGZ8+exYIFC25LkgcFBeHAgQNIS0tDXl4e1Go15s+fj40bN+LcuXM4efIkNm/erNcfutnzzz+Ps2fPYs6cOUhJScH333+PVatW6W3D/gIRmZPx48fD09MTI0eOxO7du3XfR1966SVcvHixQfvo1KkThg4diueeew4HDhzA4cOH8cwzz+jNgFu1ahW++uornDhxAhcuXMB3330HOzs7BAYG3nHf9X1fbojG7qMp1/nNmzfj008/RVJSEtLT0/HNN99ArVbXWpZ3+/btePXVV/H+++/D09MT2dnZyM7ORmFhIYCG9++IiFpahw4dMHLkSEydOhV79uzB0aNH8eSTT8Lf3x8jR44EAMyaNQvbtm1DamoqEhMTsWPHDt138MDAQMhkMmzevBlXrlzRVS5sDvWNozg6OmLKlCmYM2cO/vrrL5w4cQKTJk3STdLQuu+++7B06VIcOXIECQkJeP755/Vuxm2O9rMh7ZNCocCkSZMwd+5cdOjQodal/ch8MClOBOhmP6tUKjzwwAMICwvDrFmz4OrqetvF+lbOzs7YtWsXHnzwQXTs2BH//ve/sWTJEgwbNky3zZAhQ9ChQwcMGDAAY8eOxYgRI7Bw4ULd62+//TbmzZuHRYsWITQ0FEOHDsXvv//eoPLrWo8++iiGDh2KwYMHw8vLC2vXrq1z28rKSnz33Xd49NFH69zXN998U2upxJdeegmzZ8/Gyy+/jLCwMGzduhWbNm1Chw4dGhxrt27dsHPnTpw5cwb9+/dH9+7dMX/+fPj5+TV4H0RkmhwdHdG3b1989NFHupty5s2bh6lTp2Lp0qWQyWTYuHEj3NzcMGDAAERHRyM4OBjr1q3T7WPQoEH46aefsGnTJkREROC+++7DwYMHa/194eHh+PDDD7F48WJ07doVa9aswaJFi+46/piYGGzevBl//vknevfujXvuuQcfffTRbQNN48ePx6lTp+Dv749+/frpvdbS13ygYechKioKzz//PMaOHQsvLy+89957AIC33noLaWlpaNeuna48VXNcxx0dHfHbb7/h+PHj6N69O/71r3/ddnPDc889h0ceeQRjx45F3759kZ+frzdrvKGWLVuGxx57DC+88AJCQkIwdepUlJaWAmjadwAiskzt2rXDkSNHEB0djblz5yI8PBy9evXCZ599hldeeQVvv/12g9qzu/HNN9/A29sbAwYMwD/+8Q9MnToVTk5ODSrx2pCYYmJiMG/ePLz66qvo3bs3iouLMWHCBL39vPLKK1AoFOjcuTO8vLyQkZEBpVKJuXPnolu3bhgwYAAUCgV++OGHWuNo06YNfvnlF2zYsAHh4eFYvnw53n33Xb1t2F8gInNib2+PXbt2oU2bNnjkkUcQGhqKKVOmoLy8vFEz31auXAk/Pz8MHDgQjzzyCJ599lm0atVK97qrqyu+/PJL9OvXD926dcP27dvx22+/3TY7ujZ3+r7cUI3ZR1Ou866urli/fj3uu+8+hIaGYvny5Vi7di26dOly27Z79uyBSqXC888/D19fX91DO7Gjof07IiIprFy5Ej179sTDDz+MyMhICCGwZcsWXeJYpVJh+vTpurGljh074r///S8AwN/fH2+++SZef/11eHt7N+vNpQ0ZR3n//ffRv39/DB8+HNHR0bj33ntvW5t8yZIlCAgIQP/+/fHEE0/glVde0Stb3lztZ0PaJ+3ygg2pSkmmTSZuLdpPRM1q0qRJKCgowIYNG6QOhYiIiIiIzMjFixcREBCA7du3Y8iQIVKHQ0REREREVKtBgwYhIiICH3/8sdSh3Gb37t0YMmQIMjMz4e3tLXU4ZEBcOJGIiIiIiIjIBPz1118oKSlBWFgYsrKy8OqrryIoKAgDBgyQOjQiIiIiIiKTUlFRgStXrmDhwoUYPXo0E+IWgDUhieqRkZEBR0fHOh8ZGRlSh1irNWvW1BlzbSWliIgI6NKlS53XzjVr1kgdXp14zScisgxVVVV444030KVLF/zjH/+Al5cX4uPjYW1tzbaAiIhqtXv37juOaxkbU4uXiMgUmGqOw9DWrl2LwMBAFBQU6JYRJPPG8ulE9aiurkZaWlqdrwcFBcHKyviKLhQXFyMnJ6fW16ytrbk+EhFRLdLT01FVVVXra97e3nBycmrhiBqG13wiImJbQEREtbl+/TouXbpU5+vt27dvwWjqZ2rxEhGZAlPNcRA1NybFiYiIiIiIiIiIiIiIiIjIbLF8OhERERERERERERERERERmS0mxYmIiIiIiIiIiIiIiIiIyGwxKU5ERERERERERERERERERGaLSXEiIiIiIiIiIiIiIiIiIjJbTIoTEREREREREREREREREZHZYlKciIiIiIiIiIiIiIiIiIjMFpPiRERERERERERERERERERktv4fIMbJiUOQN64AAAAASUVORK5CYII=\n"}, "metadata": {}}], "execution_count": 13}, {"cell_type": "markdown", "source": "<h2><center>Feature Engineering</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "train['Stage_fear'] = train['Stage_fear'].map({'Yes': 1, 'No':0})\ntest['Stage_fear'] = test['Stage_fear'].map({'Yes': 1, \"No\": 0})\ntrain['Drained_after_socializing'] = train['Drained_after_socializing'].map({'Yes': 1, \"No\": 0})\ntest['Drained_after_socializing'] = test['Drained_after_socializing'].map({'Yes': 1, \"No\": 0})\noriginal['Stage_fear'] = original['Stage_fear'].map({'Yes': 1, \"No\": 0})\noriginal['Drained_after_socializing'] = original['Drained_after_socializing'].map({'Yes': 1, \"No\": 0})", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:03.590951Z", "iopub.execute_input": "2025-07-31T09:57:03.591194Z", "iopub.status.idle": "2025-07-31T09:57:03.609796Z", "shell.execute_reply.started": "2025-07-31T09:57:03.591175Z", "shell.execute_reply": "2025-07-31T09:57:03.609015Z"}}, "outputs": [], "execution_count": 14}, {"cell_type": "code", "source": "train['Personality'] = train['Personality'].map({'Extrovert': 0, 'Introvert': 1})\noriginal['Personality'] = original['Personality'].map({'Extrovert': 0, 'Introvert': 1})", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:03.610801Z", "iopub.execute_input": "2025-07-31T09:57:03.611078Z", "iopub.status.idle": "2025-07-31T09:57:03.618518Z", "shell.execute_reply.started": "2025-07-31T09:57:03.611054Z", "shell.execute_reply": "2025-07-31T09:57:03.617771Z"}}, "outputs": [], "execution_count": 15}, {"cell_type": "markdown", "source": "<h2> <center> Imputation - MICE</center> </h2>\n\nWe try imputation using MICE and also impute the missing values as 0 and see if the accuracy increases for the models.", "metadata": {}}, {"cell_type": "code", "source": "# MICE Imputation\n# ```````````````\nimputer = IterativeImputer(max_iter=50, random_state=0)\nimputed_data = imputer.fit_transform(original)\nimp_original = pd.DataFrame(imputed_data, columns=train.columns)\n\n# Fill 0\n# ``````\n# imp_original = original.fillna(-1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:03.619785Z", "iopub.execute_input": "2025-07-31T09:57:03.620070Z", "iopub.status.idle": "2025-07-31T09:57:03.722459Z", "shell.execute_reply.started": "2025-07-31T09:57:03.620045Z", "shell.execute_reply": "2025-07-31T09:57:03.721872Z"}}, "outputs": [], "execution_count": 16}, {"cell_type": "code", "source": "# MICE Imputation\n# ```````````````\nimputer = IterativeImputer(max_iter=50, random_state=0)\nimputed_data = imputer.fit_transform(train)\nimp_train = pd.DataFrame(imputed_data, columns=train.columns)\n\n# # Fill 0\n# # ``````\n# imp_train = train.fillna(-1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:03.722961Z", "iopub.execute_input": "2025-07-31T09:57:03.723155Z", "iopub.status.idle": "2025-07-31T09:57:05.796195Z", "shell.execute_reply.started": "2025-07-31T09:57:03.723139Z", "shell.execute_reply": "2025-07-31T09:57:05.795641Z"}}, "outputs": [], "execution_count": 17}, {"cell_type": "code", "source": "# MICE Imputation\n# ```````````````\nimputer = IterativeImputer(max_iter=50, random_state=0)\nimputed_data = imputer.fit_transform(test)\nimp_test = pd.DataFrame(imputed_data, columns=test.columns)\n\n# Fill 0\n# ``````\n# imp_test = test.fillna(-1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:05.796995Z", "iopub.execute_input": "2025-07-31T09:57:05.797431Z", "iopub.status.idle": "2025-07-31T09:57:06.162878Z", "shell.execute_reply.started": "2025-07-31T09:57:05.797394Z", "shell.execute_reply": "2025-07-31T09:57:06.162372Z"}}, "outputs": [], "execution_count": 18}, {"cell_type": "markdown", "source": "<h2><center>Train and Test Data</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "fin_train = pd.concat([imp_train, imp_original, imp_original], axis = 0)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.163399Z", "iopub.execute_input": "2025-07-31T09:57:06.163671Z", "iopub.status.idle": "2025-07-31T09:57:06.168312Z", "shell.execute_reply.started": "2025-07-31T09:57:06.163645Z", "shell.execute_reply": "2025-07-31T09:57:06.167704Z"}}, "outputs": [], "execution_count": 19}, {"cell_type": "code", "source": "X = imp_train.drop(imp_train.columns[-1], axis = 1)\ny = imp_train[imp_train.columns[-1]]", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.168860Z", "iopub.execute_input": "2025-07-31T09:57:06.169134Z", "iopub.status.idle": "2025-07-31T09:57:06.186875Z", "shell.execute_reply.started": "2025-07-31T09:57:06.169104Z", "shell.execute_reply": "2025-07-31T09:57:06.186269Z"}}, "outputs": [], "execution_count": 20}, {"cell_type": "code", "source": "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size = 0.25, random_state = 1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.187526Z", "iopub.execute_input": "2025-07-31T09:57:06.187765Z", "iopub.status.idle": "2025-07-31T09:57:06.204312Z", "shell.execute_reply.started": "2025-07-31T09:57:06.187747Z", "shell.execute_reply": "2025-07-31T09:57:06.203638Z"}}, "outputs": [], "execution_count": 21}, {"cell_type": "markdown", "source": "<h2><center>Logistic Regression - 0.974089</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "lr = LogisticRegression()\nlr.fit(X_train, y_train)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.209244Z", "iopub.execute_input": "2025-07-31T09:57:06.209478Z", "iopub.status.idle": "2025-07-31T09:57:06.405916Z", "shell.execute_reply.started": "2025-07-31T09:57:06.209461Z", "shell.execute_reply": "2025-07-31T09:57:06.404220Z"}}, "outputs": [{"execution_count": 22, "output_type": "execute_result", "data": {"text/plain": "LogisticRegression()", "text/html": "<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression()</pre></div></div></div></div></div>"}, "metadata": {}}], "execution_count": 22}, {"cell_type": "code", "source": "pred = lr.predict(X_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.406691Z", "iopub.execute_input": "2025-07-31T09:57:06.406907Z", "iopub.status.idle": "2025-07-31T09:57:06.413565Z", "shell.execute_reply.started": "2025-07-31T09:57:06.406890Z", "shell.execute_reply": "2025-07-31T09:57:06.412901Z"}}, "outputs": [], "execution_count": 23}, {"cell_type": "code", "source": "print(\"Logistic Regression Accuracy : \", accuracy_score(pred, y_test))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.414326Z", "iopub.execute_input": "2025-07-31T09:57:06.416203Z", "iopub.status.idle": "2025-07-31T09:57:06.441729Z", "shell.execute_reply.started": "2025-07-31T09:57:06.416180Z", "shell.execute_reply": "2025-07-31T09:57:06.440854Z"}}, "outputs": [{"name": "stdout", "text": "Logistic Regression Accuracy :  0.9686892679766789\n", "output_type": "stream"}], "execution_count": 24}, {"cell_type": "code", "source": "off_pred_probs['LR'] = lr.predict(X)\ntest_pred_probs['LR'] = lr.predict(imp_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.442779Z", "iopub.execute_input": "2025-07-31T09:57:06.443707Z", "iopub.status.idle": "2025-07-31T09:57:06.467542Z", "shell.execute_reply.started": "2025-07-31T09:57:06.443684Z", "shell.execute_reply": "2025-07-31T09:57:06.467018Z"}}, "outputs": [], "execution_count": 25}, {"cell_type": "code", "source": "pred = lr.predict(imp_test)\nsam_lr = sam_sub.copy()\nsam_lr['Personality'] = pred\nsam_lr['Personality'] = sam_lr['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_lr.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.467995Z", "iopub.execute_input": "2025-07-31T09:57:06.468175Z", "iopub.status.idle": "2025-07-31T09:57:06.517274Z", "shell.execute_reply.started": "2025-07-31T09:57:06.468158Z", "shell.execute_reply": "2025-07-31T09:57:06.516762Z"}}, "outputs": [{"execution_count": 26, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 26}, {"cell_type": "code", "source": "sam_lr.to_csv(\"Logistic_sub.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.517736Z", "iopub.execute_input": "2025-07-31T09:57:06.518360Z", "iopub.status.idle": "2025-07-31T09:57:06.532334Z", "shell.execute_reply.started": "2025-07-31T09:57:06.518339Z", "shell.execute_reply": "2025-07-31T09:57:06.531819Z"}}, "outputs": [], "execution_count": 27}, {"cell_type": "markdown", "source": "<h2><center>KNN - 0.974089</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "knn = KNeighborsClassifier(n_neighbors=10)  # You can choose k as per your use case\nknn.fit(X_train, y_train)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.533037Z", "iopub.execute_input": "2025-07-31T09:57:06.533192Z", "iopub.status.idle": "2025-07-31T09:57:06.555550Z", "shell.execute_reply.started": "2025-07-31T09:57:06.533179Z", "shell.execute_reply": "2025-07-31T09:57:06.555060Z"}}, "outputs": [{"execution_count": 28, "output_type": "execute_result", "data": {"text/plain": "KNeighborsClassifier(n_neighbors=10)", "text/html": "<style>#sk-container-id-2 {color: black;background-color: white;}#sk-container-id-2 pre{padding: 0;}#sk-container-id-2 div.sk-toggleable {background-color: white;}#sk-container-id-2 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-2 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-2 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-2 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-2 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-2 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-2 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-2 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-2 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-2 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-2 div.sk-item {position: relative;z-index: 1;}#sk-container-id-2 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-2 div.sk-item::before, #sk-container-id-2 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-2 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-2 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-2 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-2 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-2 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-2 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-2 div.sk-label-container {text-align: center;}#sk-container-id-2 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-2 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>KNeighborsClassifier(n_neighbors=10)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">KNeighborsClassifier</label><div class=\"sk-toggleable__content\"><pre>KNeighborsClassifier(n_neighbors=10)</pre></div></div></div></div></div>"}, "metadata": {}}], "execution_count": 28}, {"cell_type": "code", "source": "y_pred = knn.predict(X_test)\nprint(\"KNN Accuracy : \", accuracy_score(y_test, y_pred))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.556178Z", "iopub.execute_input": "2025-07-31T09:57:06.556430Z", "iopub.status.idle": "2025-07-31T09:57:06.816888Z", "shell.execute_reply.started": "2025-07-31T09:57:06.556406Z", "shell.execute_reply": "2025-07-31T09:57:06.816042Z"}}, "outputs": [{"name": "stdout", "text": "KNN Accuracy :  0.9678255236450011\n", "output_type": "stream"}], "execution_count": 29}, {"cell_type": "code", "source": "off_pred_probs['KNN'] = knn.predict(X)\ntest_pred_probs['KNN'] = knn.predict(imp_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:06.817827Z", "iopub.execute_input": "2025-07-31T09:57:06.818276Z", "iopub.status.idle": "2025-07-31T09:57:08.221005Z", "shell.execute_reply.started": "2025-07-31T09:57:06.818256Z", "shell.execute_reply": "2025-07-31T09:57:08.219957Z"}}, "outputs": [], "execution_count": 30}, {"cell_type": "code", "source": "pred = knn.predict(imp_test)\nsam_knn = sam_sub.copy()\nsam_knn['Personality'] = pred\nsam_knn['Personality'] = sam_knn['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_knn.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.221869Z", "iopub.execute_input": "2025-07-31T09:57:08.222087Z", "iopub.status.idle": "2025-07-31T09:57:08.578556Z", "shell.execute_reply.started": "2025-07-31T09:57:08.222067Z", "shell.execute_reply": "2025-07-31T09:57:08.577813Z"}}, "outputs": [{"execution_count": 31, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 31}, {"cell_type": "code", "source": "sam_knn.to_csv(\"KNN_sub.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.579276Z", "iopub.execute_input": "2025-07-31T09:57:08.579487Z", "iopub.status.idle": "2025-07-31T09:57:08.589735Z", "shell.execute_reply.started": "2025-07-31T09:57:08.579471Z", "shell.execute_reply": "2025-07-31T09:57:08.589101Z"}}, "outputs": [], "execution_count": 32}, {"cell_type": "markdown", "source": "<h2><center>Extreme Gradient Boosting - 0.974089</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "# def objective(trial):\n#     train_x, valid_x, train_y, valid_y = train_test_split(X, y, test_size=0.25)\n#     params = {\n#         \"objective\": \"binary:logistic\",\n#         \"booster\": trial.suggest_categorical(\"booster\", [\"gbtree\", \"gblinear\", \"dart\"]),\n#         \"lambda\": trial.suggest_float(\"lambda\", 1e-8, 1.0, log=True),\n#         \"alpha\": trial.suggest_float(\"alpha\", 1e-8, 1.0, log=True),\n#         \"max_depth\": trial.suggest_int(\"max\n#         _depth\", 2, 12),\n#         \"eta\": trial.suggest_float(\"eta\", 1e-4, 1.0, log=True),\n#         \"subsample\": trial.suggest_float(\"subsample\", 0.5, 1.0),\n#         \"colsample_bytree\": trial.suggest_float(\"colsample_bytree\", 0.5, 1.0),\n#     }\n#     dtrain = xgb.DMatrix(train_x, label=train_y)\n#     dvalid = xgb.DMatrix(valid_x, label=valid_y)\n#     bst = xgb.train(params, dtrain, evals=[(dvalid, \"eval\")], num_boost_round=100, early_stopping_rounds=10, verbose_eval=False)\n#     preds = bst.predict(dvalid)\n#     pred_labels = (preds > 0.5).astype(int)\n#     accuracy = accuracy_score(valid_y, pred_labels)\n#     return accuracy", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.590329Z", "iopub.execute_input": "2025-07-31T09:57:08.590502Z", "iopub.status.idle": "2025-07-31T09:57:08.603133Z", "shell.execute_reply.started": "2025-07-31T09:57:08.590488Z", "shell.execute_reply": "2025-07-31T09:57:08.602513Z"}}, "outputs": [], "execution_count": 33}, {"cell_type": "code", "source": "# ## Hyperparameter Tuning using Bayesian Search\n# optuna.logging.set_verbosity(optuna.logging.WARNING)\n# study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler())\n# study.optimize(objective, n_trials=250, show_progress_bar = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.603786Z", "iopub.execute_input": "2025-07-31T09:57:08.603944Z", "iopub.status.idle": "2025-07-31T09:57:08.617680Z", "shell.execute_reply.started": "2025-07-31T09:57:08.603931Z", "shell.execute_reply": "2025-07-31T09:57:08.617101Z"}}, "outputs": [], "execution_count": 34}, {"cell_type": "code", "source": "# # Print the best result\n# print(f'Best trial accuracy: {study.best_trial.value}')\n# print(f'Best hyperparameters: {study.best_trial.params}')", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.618416Z", "iopub.execute_input": "2025-07-31T09:57:08.618709Z", "iopub.status.idle": "2025-07-31T09:57:08.636667Z", "shell.execute_reply.started": "2025-07-31T09:57:08.618686Z", "shell.execute_reply": "2025-07-31T09:57:08.636064Z"}}, "outputs": [], "execution_count": 35}, {"cell_type": "code", "source": "# if study.best_trial.value >= 0.9771107752105377:\n#     params = study.best_trial.params\n# else:\n#     params = {'booster': 'gbtree', \n#           'lambda': 0.005553898958169121, \n#           'alpha': 0.030364530410431377, \n#           'max_depth': 6, \n#           'eta': 0.6076967918765325, \n#           'subsample': 0.9540478939663687, \n#           'colsample_bytree': 0.8426790562963757\n#          }", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.637415Z", "iopub.execute_input": "2025-07-31T09:57:08.637839Z", "iopub.status.idle": "2025-07-31T09:57:08.651875Z", "shell.execute_reply.started": "2025-07-31T09:57:08.637816Z", "shell.execute_reply": "2025-07-31T09:57:08.651192Z"}}, "outputs": [], "execution_count": 36}, {"cell_type": "code", "source": "params = {'booster': 'gbtree', \n          'lambda': 0.005553898958169121, \n          'alpha': 0.030364530410431377, \n          'max_depth': 6, \n          'eta': 0.6076967918765325, \n          'subsample': 0.9540478939663687, \n          'colsample_bytree': 0.8426790562963757\n         }", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.652696Z", "iopub.execute_input": "2025-07-31T09:57:08.653320Z", "iopub.status.idle": "2025-07-31T09:57:08.669353Z", "shell.execute_reply.started": "2025-07-31T09:57:08.653296Z", "shell.execute_reply": "2025-07-31T09:57:08.668719Z"}}, "outputs": [], "execution_count": 37}, {"cell_type": "code", "source": "train_x, valid_x, train_y, valid_y = train_test_split(X, y, test_size=0.25)\ndtrain = xgb.DMatrix(train_x, label=train_y)\ndvalid = xgb.DMatrix(valid_x, label=valid_y)\nbst = xgb.train(params, dtrain, \n                evals=[(dvalid, \"eval\")], \n                num_boost_round=100, \n                early_stopping_rounds=10, \n                verbose_eval=False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.670093Z", "iopub.execute_input": "2025-07-31T09:57:08.670327Z", "iopub.status.idle": "2025-07-31T09:57:08.800202Z", "shell.execute_reply.started": "2025-07-31T09:57:08.670305Z", "shell.execute_reply": "2025-07-31T09:57:08.799430Z"}}, "outputs": [], "execution_count": 38}, {"cell_type": "code", "source": "preds = bst.predict(dvalid)\npred_labels = (preds > 0.5).astype(int)\npred_labels\nprint(\"XGB Accuracy:\", accuracy_score(valid_y, pred_labels))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.801170Z", "iopub.execute_input": "2025-07-31T09:57:08.801921Z", "iopub.status.idle": "2025-07-31T09:57:08.808963Z", "shell.execute_reply.started": "2025-07-31T09:57:08.801892Z", "shell.execute_reply": "2025-07-31T09:57:08.808219Z"}}, "outputs": [{"name": "stdout", "text": "XGB Accuracy: 0.9714964370546318\n", "output_type": "stream"}], "execution_count": 39}, {"cell_type": "code", "source": "off_pred_probs['XGB'] = bst.predict(xgb.DMatrix(X))\ntest_pred_probs['XGB'] = bst.predict(xgb.DMatrix(imp_test))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.809645Z", "iopub.execute_input": "2025-07-31T09:57:08.809825Z", "iopub.status.idle": "2025-07-31T09:57:08.835962Z", "shell.execute_reply.started": "2025-07-31T09:57:08.809810Z", "shell.execute_reply": "2025-07-31T09:57:08.835442Z"}}, "outputs": [], "execution_count": 40}, {"cell_type": "code", "source": "Xgb_test = xgb.DMatrix(imp_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.836609Z", "iopub.execute_input": "2025-07-31T09:57:08.836789Z", "iopub.status.idle": "2025-07-31T09:57:08.841336Z", "shell.execute_reply.started": "2025-07-31T09:57:08.836774Z", "shell.execute_reply": "2025-07-31T09:57:08.840763Z"}}, "outputs": [], "execution_count": 41}, {"cell_type": "code", "source": "preds = bst.predict(Xgb_test)\npred_labels = (preds > 0.5).astype(int)\nsam_xgb = sam_sub.copy()\nsam_xgb['Personality'] = pred_labels\nsam_xgb['Personality'] = sam_xgb['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_xgb.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.841961Z", "iopub.execute_input": "2025-07-31T09:57:08.842153Z", "iopub.status.idle": "2025-07-31T09:57:08.862668Z", "shell.execute_reply.started": "2025-07-31T09:57:08.842138Z", "shell.execute_reply": "2025-07-31T09:57:08.861869Z"}}, "outputs": [{"execution_count": 42, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 42}, {"cell_type": "code", "source": "sam_xgb.to_csv(\"XGB_sub.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.863531Z", "iopub.execute_input": "2025-07-31T09:57:08.863921Z", "iopub.status.idle": "2025-07-31T09:57:08.879618Z", "shell.execute_reply.started": "2025-07-31T09:57:08.863894Z", "shell.execute_reply": "2025-07-31T09:57:08.878922Z"}}, "outputs": [], "execution_count": 43}, {"cell_type": "markdown", "source": "<h2><center>Light Gradient Boosting Machine</center></h2>\n\n1. Gradient Boosting Decision Trees - GBDT\n2. Gradient-Based One-Side Sampling - GOSS", "metadata": {}}, {"cell_type": "code", "source": "lgbm_params = {\n    \"boosting_type\": \"gbdt\",\n    \"device\": \"gpu\",\n    \"colsample_bytree\": 0.4366677273946288,\n    \"learning_rate\": 0.016164161953515117,\n    \"max_depth\": 12,\n    \"min_child_samples\": 67,\n    \"n_estimators\": 10000,\n    \"n_jobs\": -1,\n    \"num_leaves\": 243,\n    \"random_state\": 42,\n    \"reg_alpha\": 6.38288560443373,\n    \"reg_lambda\": 9.392999314379155,\n    \"subsample\": 0.7989164499431718,\n    \"verbose\": -1,\n    \"callbacks\": [\n        log_evaluation(period=1000), \n        early_stopping(stopping_rounds=100)\n    ]\n}\nlgbm_goss_params = {\n    \"boosting_type\": \"goss\",\n    \"device\": \"gpu\",\n    \"colsample_bytree\": 0.32751831793031183,\n    \"learning_rate\": 0.006700715059604966,\n    \"max_depth\": 12,\n    \"min_child_samples\": 84,\n    \"n_estimators\": 10000,\n    \"n_jobs\": -1,\n    \"num_leaves\": 229,\n    \"random_state\": 42,\n    \"reg_alpha\": 6.879977008084246,\n    \"reg_lambda\": 4.739518466581721,\n    \"subsample\": 0.5411572049978781,\n    \"verbosity\": -1,\n    \"callbacks\": [\n        log_evaluation(period=1000), \n        early_stopping(stopping_rounds=100)\n    ]\n}", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.880316Z", "iopub.execute_input": "2025-07-31T09:57:08.880480Z", "iopub.status.idle": "2025-07-31T09:57:08.889158Z", "shell.execute_reply.started": "2025-07-31T09:57:08.880464Z", "shell.execute_reply": "2025-07-31T09:57:08.888549Z"}}, "outputs": [], "execution_count": 44}, {"cell_type": "code", "source": "lgbm_model = LGBMClassifier(**lgbm_params, verbosity = -1)\nlgbm_goss_model = LGBMClassifier(**lgbm_goss_params)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.889611Z", "iopub.execute_input": "2025-07-31T09:57:08.889811Z", "iopub.status.idle": "2025-07-31T09:57:08.903958Z", "shell.execute_reply.started": "2025-07-31T09:57:08.889796Z", "shell.execute_reply": "2025-07-31T09:57:08.903109Z"}}, "outputs": [], "execution_count": 45}, {"cell_type": "code", "source": "train_x, valid_x, train_y, valid_y = train_test_split(X, y, test_size=0.25)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.904604Z", "iopub.execute_input": "2025-07-31T09:57:08.904814Z", "iopub.status.idle": "2025-07-31T09:57:08.923931Z", "shell.execute_reply.started": "2025-07-31T09:57:08.904800Z", "shell.execute_reply": "2025-07-31T09:57:08.923286Z"}}, "outputs": [], "execution_count": 46}, {"cell_type": "code", "source": "lgbm_model.fit(train_x, train_y)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:08.924560Z", "iopub.execute_input": "2025-07-31T09:57:08.924881Z", "iopub.status.idle": "2025-07-31T09:57:18.636126Z", "shell.execute_reply.started": "2025-07-31T09:57:08.924845Z", "shell.execute_reply": "2025-07-31T09:57:18.635488Z"}}, "outputs": [{"name": "stderr", "text": "1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n1 warning generated.\n", "output_type": "stream"}, {"execution_count": 47, "output_type": "execute_result", "data": {"text/plain": "LGBMClassifier(callbacks=[<lightgbm.callback._LogEvaluationCallback object at 0x7e02554d9a90>,\n                          <lightgbm.callback._EarlyStoppingCallback object at 0x7e0255474790>],\n               colsample_bytree=0.4366677273946288, device='gpu',\n               learning_rate=0.016164161953515117, max_depth=12,\n               min_child_samples=67, n_estimators=10000, n_jobs=-1,\n               num_leaves=243, random_state=42, reg_alpha=6.38288560443373,\n               reg_lambda=9.392999314379155, subsample=0.7989164499431718,\n               verbose=-1, verbosity=-1)", "text/html": "<style>#sk-container-id-3 {color: black;background-color: white;}#sk-container-id-3 pre{padding: 0;}#sk-container-id-3 div.sk-toggleable {background-color: white;}#sk-container-id-3 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-3 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-3 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-3 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-3 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-3 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-3 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-3 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-3 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-3 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-3 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-3 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-3 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-3 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-3 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-3 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-3 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-3 div.sk-item {position: relative;z-index: 1;}#sk-container-id-3 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-3 div.sk-item::before, #sk-container-id-3 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-3 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-3 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-3 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-3 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-3 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-3 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-3 div.sk-label-container {text-align: center;}#sk-container-id-3 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-3 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-3\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LGBMClassifier(callbacks=[&lt;lightgbm.callback._LogEvaluationCallback object at 0x7e02554d9a90&gt;,\n                          &lt;lightgbm.callback._EarlyStoppingCallback object at 0x7e0255474790&gt;],\n               colsample_bytree=0.4366677273946288, device=&#x27;gpu&#x27;,\n               learning_rate=0.016164161953515117, max_depth=12,\n               min_child_samples=67, n_estimators=10000, n_jobs=-1,\n               num_leaves=243, random_state=42, reg_alpha=6.38288560443373,\n               reg_lambda=9.392999314379155, subsample=0.7989164499431718,\n               verbose=-1, verbosity=-1)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-3\" type=\"checkbox\" checked><label for=\"sk-estimator-id-3\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LGBMClassifier</label><div class=\"sk-toggleable__content\"><pre>LGBMClassifier(callbacks=[&lt;lightgbm.callback._LogEvaluationCallback object at 0x7e02554d9a90&gt;,\n                          &lt;lightgbm.callback._EarlyStoppingCallback object at 0x7e0255474790&gt;],\n               colsample_bytree=0.4366677273946288, device=&#x27;gpu&#x27;,\n               learning_rate=0.016164161953515117, max_depth=12,\n               min_child_samples=67, n_estimators=10000, n_jobs=-1,\n               num_leaves=243, random_state=42, reg_alpha=6.38288560443373,\n               reg_lambda=9.392999314379155, subsample=0.7989164499431718,\n               verbose=-1, verbosity=-1)</pre></div></div></div></div></div>"}, "metadata": {}}], "execution_count": 47}, {"cell_type": "code", "source": "lgbm_goss_model.fit(train_x, train_y)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:57:18.636569Z", "iopub.execute_input": "2025-07-31T09:57:18.636810Z", "iopub.status.idle": "2025-07-31T09:58:19.025941Z", "shell.execute_reply.started": "2025-07-31T09:57:18.636792Z", "shell.execute_reply": "2025-07-31T09:58:19.025146Z"}}, "outputs": [{"execution_count": 48, "output_type": "execute_result", "data": {"text/plain": "LGBMClassifier(boosting_type='goss',\n               callbacks=[<lightgbm.callback._LogEvaluationCallback object at 0x7e02574ea190>,\n                          <lightgbm.callback._EarlyStoppingCallback object at 0x7e02554d8650>],\n               colsample_bytree=0.32751831793031183, device='gpu',\n               learning_rate=0.006700715059604966, max_depth=12,\n               min_child_samples=84, n_estimators=10000, n_jobs=-1,\n               num_leaves=229, random_state=42, reg_alpha=6.879977008084246,\n               reg_lambda=4.739518466581721, subsample=0.5411572049978781,\n               verbosity=-1)", "text/html": "<style>#sk-container-id-4 {color: black;background-color: white;}#sk-container-id-4 pre{padding: 0;}#sk-container-id-4 div.sk-toggleable {background-color: white;}#sk-container-id-4 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-4 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-4 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-4 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-4 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-4 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-4 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-4 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-4 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-4 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-4 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-4 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-4 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-4 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-4 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-4 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-4 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-4 div.sk-item {position: relative;z-index: 1;}#sk-container-id-4 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-4 div.sk-item::before, #sk-container-id-4 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-4 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-4 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-4 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-4 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-4 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-4 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-4 div.sk-label-container {text-align: center;}#sk-container-id-4 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-4 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-4\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LGBMClassifier(boosting_type=&#x27;goss&#x27;,\n               callbacks=[&lt;lightgbm.callback._LogEvaluationCallback object at 0x7e02574ea190&gt;,\n                          &lt;lightgbm.callback._EarlyStoppingCallback object at 0x7e02554d8650&gt;],\n               colsample_bytree=0.32751831793031183, device=&#x27;gpu&#x27;,\n               learning_rate=0.006700715059604966, max_depth=12,\n               min_child_samples=84, n_estimators=10000, n_jobs=-1,\n               num_leaves=229, random_state=42, reg_alpha=6.879977008084246,\n               reg_lambda=4.739518466581721, subsample=0.5411572049978781,\n               verbosity=-1)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-4\" type=\"checkbox\" checked><label for=\"sk-estimator-id-4\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LGBMClassifier</label><div class=\"sk-toggleable__content\"><pre>LGBMClassifier(boosting_type=&#x27;goss&#x27;,\n               callbacks=[&lt;lightgbm.callback._LogEvaluationCallback object at 0x7e02574ea190&gt;,\n                          &lt;lightgbm.callback._EarlyStoppingCallback object at 0x7e02554d8650&gt;],\n               colsample_bytree=0.32751831793031183, device=&#x27;gpu&#x27;,\n               learning_rate=0.006700715059604966, max_depth=12,\n               min_child_samples=84, n_estimators=10000, n_jobs=-1,\n               num_leaves=229, random_state=42, reg_alpha=6.879977008084246,\n               reg_lambda=4.739518466581721, subsample=0.5411572049978781,\n               verbosity=-1)</pre></div></div></div></div></div>"}, "metadata": {}}], "execution_count": 48}, {"cell_type": "code", "source": "preds = lgbm_model.predict(valid_x)\npred_labels = (preds > 0.5).astype(int)\npred_labels\nprint(\"LGBM GBDT Accuracy:\", accuracy_score(valid_y, pred_labels))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:19.026815Z", "iopub.execute_input": "2025-07-31T09:58:19.027510Z", "iopub.status.idle": "2025-07-31T09:58:19.253946Z", "shell.execute_reply.started": "2025-07-31T09:58:19.027489Z", "shell.execute_reply": "2025-07-31T09:58:19.253195Z"}}, "outputs": [{"name": "stdout", "text": "LGBM GBDT Accuracy: 0.9710645648887929\n", "output_type": "stream"}], "execution_count": 49}, {"cell_type": "code", "source": "off_pred_probs['LGBM_GBDT'] = lgbm_model.predict(X)\ntest_pred_probs['LGBM_GBDT'] = lgbm_model.predict(imp_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:19.254406Z", "iopub.execute_input": "2025-07-31T09:58:19.254593Z", "iopub.status.idle": "2025-07-31T09:58:20.384392Z", "shell.execute_reply.started": "2025-07-31T09:58:19.254562Z", "shell.execute_reply": "2025-07-31T09:58:20.383822Z"}}, "outputs": [], "execution_count": 50}, {"cell_type": "code", "source": "preds = lgbm_goss_model.predict(valid_x)\npred_labels = (preds > 0.5).astype(int)\npred_labels\nprint(\"LGBM goss Accuracy:\", accuracy_score(valid_y, pred_labels))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:20.385778Z", "iopub.execute_input": "2025-07-31T09:58:20.386285Z", "iopub.status.idle": "2025-07-31T09:58:21.342889Z", "shell.execute_reply.started": "2025-07-31T09:58:20.386265Z", "shell.execute_reply": "2025-07-31T09:58:21.342329Z"}}, "outputs": [{"name": "stdout", "text": "LGBM goss Accuracy: 0.9702008205571151\n", "output_type": "stream"}], "execution_count": 51}, {"cell_type": "code", "source": "off_pred_probs['LGBM_GOSS'] = lgbm_goss_model.predict(X)\ntest_pred_probs['LGBM_GOSS'] = lgbm_goss_model.predict(imp_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:21.343737Z", "iopub.execute_input": "2025-07-31T09:58:21.343924Z", "iopub.status.idle": "2025-07-31T09:58:26.602546Z", "shell.execute_reply.started": "2025-07-31T09:58:21.343907Z", "shell.execute_reply": "2025-07-31T09:58:26.601985Z"}}, "outputs": [], "execution_count": 52}, {"cell_type": "code", "source": "preds = lgbm_model.predict(imp_test)\npred_labels = (preds > 0.5).astype(int)\nsam_lgbm = sam_sub.copy()\nsam_lgbm['Personality'] = pred_labels\nsam_lgbm['Personality'] = sam_lgbm['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_lgbm.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:26.603765Z", "iopub.execute_input": "2025-07-31T09:58:26.604295Z", "iopub.status.idle": "2025-07-31T09:58:26.897062Z", "shell.execute_reply.started": "2025-07-31T09:58:26.604273Z", "shell.execute_reply": "2025-07-31T09:58:26.896485Z"}}, "outputs": [{"execution_count": 53, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 53}, {"cell_type": "code", "source": "sam_lgbm.to_csv(\"LGBM_GBDT_sub.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:26.899004Z", "iopub.execute_input": "2025-07-31T09:58:26.900038Z", "iopub.status.idle": "2025-07-31T09:58:26.910891Z", "shell.execute_reply.started": "2025-07-31T09:58:26.900018Z", "shell.execute_reply": "2025-07-31T09:58:26.910216Z"}}, "outputs": [], "execution_count": 54}, {"cell_type": "code", "source": "preds = lgbm_goss_model.predict(imp_test)\npred_labels = (preds > 0.5).astype(int)\nsam_lgbm_goss = sam_sub.copy()\nsam_lgbm_goss['Personality'] = pred_labels\nsam_lgbm_goss['Personality'] = sam_lgbm_goss['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_lgbm_goss.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:26.911535Z", "iopub.execute_input": "2025-07-31T09:58:26.911737Z", "iopub.status.idle": "2025-07-31T09:58:28.203504Z", "shell.execute_reply.started": "2025-07-31T09:58:26.911723Z", "shell.execute_reply": "2025-07-31T09:58:28.202372Z"}}, "outputs": [{"execution_count": 55, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 55}, {"cell_type": "code", "source": "sam_lgbm_goss.to_csv(\"LGBM_GOSS_sub.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.204159Z", "iopub.execute_input": "2025-07-31T09:58:28.204355Z", "iopub.status.idle": "2025-07-31T09:58:28.215967Z", "shell.execute_reply.started": "2025-07-31T09:58:28.204323Z", "shell.execute_reply": "2025-07-31T09:58:28.215534Z"}}, "outputs": [], "execution_count": 56}, {"cell_type": "markdown", "source": "<h2><center>ANN (Optuna + High Epochs) - 0.975708</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.216425Z", "iopub.execute_input": "2025-07-31T09:58:28.216673Z", "iopub.status.idle": "2025-07-31T09:58:28.225911Z", "shell.execute_reply.started": "2025-07-31T09:58:28.216656Z", "shell.execute_reply": "2025-07-31T09:58:28.225272Z"}}, "outputs": [], "execution_count": 57}, {"cell_type": "code", "source": "X_train = torch.from_numpy(X_train.to_numpy().astype(np.float32))\ny_train = torch.from_numpy(y_train.to_numpy().astype(np.int64))\nX_test  = torch.from_numpy(X_test.to_numpy().astype(np.float32))\ny_test  = torch.from_numpy(y_test.to_numpy().astype(np.int64))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.226682Z", "iopub.execute_input": "2025-07-31T09:58:28.226925Z", "iopub.status.idle": "2025-07-31T09:58:28.248566Z", "shell.execute_reply.started": "2025-07-31T09:58:28.226899Z", "shell.execute_reply": "2025-07-31T09:58:28.248006Z"}}, "outputs": [], "execution_count": 58}, {"cell_type": "code", "source": "test_imp = torch.from_numpy(imp_test.to_numpy().astype(np.float32))", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.249559Z", "iopub.execute_input": "2025-07-31T09:58:28.249823Z", "iopub.status.idle": "2025-07-31T09:58:28.262019Z", "shell.execute_reply.started": "2025-07-31T09:58:28.249802Z", "shell.execute_reply": "2025-07-31T09:58:28.261307Z"}}, "outputs": [], "execution_count": 59}, {"cell_type": "code", "source": "class custom_dataset(Dataset):\n    def __init__(self, X, y):\n        self.X = X\n        self.y = y\n    def __len__(self):\n        return self.X.shape[0]\n    def __getitem__(self, index):\n        return self.X[index], self.y[index]", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.262728Z", "iopub.execute_input": "2025-07-31T09:58:28.262935Z", "iopub.status.idle": "2025-07-31T09:58:28.275802Z", "shell.execute_reply.started": "2025-07-31T09:58:28.262920Z", "shell.execute_reply": "2025-07-31T09:58:28.275079Z"}}, "outputs": [], "execution_count": 60}, {"cell_type": "code", "source": "train_dataset = custom_dataset(X_train, y_train)\ntest_dataset = custom_dataset(X_test, y_test)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.276538Z", "iopub.execute_input": "2025-07-31T09:58:28.276797Z", "iopub.status.idle": "2025-07-31T09:58:28.289809Z", "shell.execute_reply.started": "2025-07-31T09:58:28.276775Z", "shell.execute_reply": "2025-07-31T09:58:28.289202Z"}}, "outputs": [], "execution_count": 61}, {"cell_type": "code", "source": "dl_train = DataLoader(train_dataset, batch_size=128, shuffle=True,)\ndl_test  = DataLoader( test_dataset, batch_size=128, shuffle=False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.290517Z", "iopub.execute_input": "2025-07-31T09:58:28.290773Z", "iopub.status.idle": "2025-07-31T09:58:28.487128Z", "shell.execute_reply.started": "2025-07-31T09:58:28.290751Z", "shell.execute_reply": "2025-07-31T09:58:28.486300Z"}}, "outputs": [], "execution_count": 62}, {"cell_type": "code", "source": "X_train.shape", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.487890Z", "iopub.execute_input": "2025-07-31T09:58:28.488135Z", "iopub.status.idle": "2025-07-31T09:58:28.505274Z", "shell.execute_reply.started": "2025-07-31T09:58:28.488117Z", "shell.execute_reply": "2025-07-31T09:58:28.504782Z"}}, "outputs": [{"execution_count": 63, "output_type": "execute_result", "data": {"text/plain": "<PERSON>.<PERSON><PERSON>([13893, 7])"}, "metadata": {}}], "execution_count": 63}, {"cell_type": "code", "source": "class Model(nn.Module):\n    def __init__(self, input_size):\n        super().__init__()\n        self.network = nn.Sequential(\n            nn.Linear(input_size, 128),\n            nn.LeakyReLU(),\n            nn.<PERSON>ar(128, 64),\n            nn.<PERSON>kyRe<PERSON><PERSON>(),\n            nn.Linear(64, 32),\n            nn.<PERSON><PERSON><PERSON>(),\n            nn.Linear(32, 10),\n            nn.<PERSON><PERSON><PERSON>(),\n            nn.Linear(10, 2)\n        )\n    def forward(self, X):\n        return self.network(X)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.506024Z", "iopub.execute_input": "2025-07-31T09:58:28.506277Z", "iopub.status.idle": "2025-07-31T09:58:28.519604Z", "shell.execute_reply.started": "2025-07-31T09:58:28.506254Z", "shell.execute_reply": "2025-07-31T09:58:28.518941Z"}}, "outputs": [], "execution_count": 64}, {"cell_type": "code", "source": "# def objective(trial):\n#     train_x, valid_x, train_y, valid_y = train_test_split(X, y, test_size=0.25)\n#     train_x = torch.from_numpy(train_x.to_numpy().astype(np.float32))\n#     train_y = torch.from_numpy(train_y.to_numpy().astype(np.int64))\n#     valid_x  = torch.from_numpy(valid_x.to_numpy().astype(np.float32))\n#     valid_y  = torch.from_numpy(valid_y.to_numpy().astype(np.int64))\n    \n#     params = {\n#         \"learning_rate\": trial.suggest_float('learning_rate', 0.0001, 0.1, log = True),\n#         \"weight_decay\": trial.suggest_float(\"weight_decay\", 1e-4, 1, log=True),\n#     }\n    \n#     model = Model(train_x.shape[1])\n#     model.load_state_dict(torch.load('/kaggle/input/introverts-5hl-nn/pytorch/default/1/model_weights.pth', map_location=device))\n#     criterion = nn.CrossEntropyLoss()\n#     optimizer = optim.SGD(model.parameters(), lr = params['learning_rate'], weight_decay = params['weight_decay'])\n#     model = nn.DataParallel(model)\n#     model.to(device)\n#     for epoch in range(10):\n#         total_epoch_loss = 0\n#         for batch_features, batch_labels in dl_train:\n#             batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)\n#             output = model(batch_features)\n#             loss = criterion(output, batch_labels)\n#             optimizer.zero_grad()\n#             loss.backward()\n#             optimizer.step()\n#             total_epoch_loss = total_epoch_loss + loss.item()\n#     model.eval()\n#     preds = torch.max(model(valid_x.to(device)), 1).indices\n#     pred_labels = preds.cpu().numpy()\n#     accuracy = accuracy_score(valid_y, pred_labels)\n#     return accuracy", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.520326Z", "iopub.execute_input": "2025-07-31T09:58:28.520561Z", "iopub.status.idle": "2025-07-31T09:58:28.534176Z", "shell.execute_reply.started": "2025-07-31T09:58:28.520540Z", "shell.execute_reply": "2025-07-31T09:58:28.533520Z"}}, "outputs": [], "execution_count": 65}, {"cell_type": "code", "source": "# ## Hyperparameter Tuning using Bayesian Search\n# optuna.logging.set_verbosity(optuna.logging.WARNING)\n# study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler())\n# study.optimize(objective, n_trials=250, show_progress_bar = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.534900Z", "iopub.execute_input": "2025-07-31T09:58:28.535143Z", "iopub.status.idle": "2025-07-31T09:58:28.554701Z", "shell.execute_reply.started": "2025-07-31T09:58:28.535120Z", "shell.execute_reply": "2025-07-31T09:58:28.553985Z"}}, "outputs": [], "execution_count": 66}, {"cell_type": "code", "source": "# Print the best result\n# print(f'Best trial accuracy: {study.best_trial.value}')\n# print(f'Best hyperparameters: {study.best_trial.params}')\n\n\n# Best trial accuracy: \n# 0.9762470308788599\n# Best hyperparameters: \n# {\n#     'learning_rate': 0.0046665002218051034, \n#     'weight_decay': 0.0034367269000430438\n# }", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.560604Z", "iopub.execute_input": "2025-07-31T09:58:28.560812Z", "iopub.status.idle": "2025-07-31T09:58:28.569528Z", "shell.execute_reply.started": "2025-07-31T09:58:28.560795Z", "shell.execute_reply": "2025-07-31T09:58:28.568843Z"}}, "outputs": [], "execution_count": 67}, {"cell_type": "code", "source": "model = Model(train.shape[1]-1)\nmodel.load_state_dict(torch.load(vars.model_path, map_location=device))\ncriterion = nn.CrossEntropyLoss()\noptimizer = optim.SGD(model.parameters(), lr = 0.0046665002218051034, weight_decay = 0.0034367269000430438)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:28.570212Z", "iopub.execute_input": "2025-07-31T09:58:28.570390Z", "iopub.status.idle": "2025-07-31T09:58:31.293448Z", "shell.execute_reply.started": "2025-07-31T09:58:28.570374Z", "shell.execute_reply": "2025-07-31T09:58:31.292867Z"}}, "outputs": [], "execution_count": 68}, {"cell_type": "code", "source": "from torchinfo import summary\n\nsummary(model)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:31.294204Z", "iopub.execute_input": "2025-07-31T09:58:31.294763Z", "iopub.status.idle": "2025-07-31T09:58:31.320879Z", "shell.execute_reply.started": "2025-07-31T09:58:31.294732Z", "shell.execute_reply": "2025-07-31T09:58:31.320312Z"}}, "outputs": [{"execution_count": 69, "output_type": "execute_result", "data": {"text/plain": "=================================================================\nLayer (type:depth-idx)                   Param #\n=================================================================\nModel                                    --\n├─Sequential: 1-1                        --\n│    └─Linear: 2-1                       1,024\n│    └─LeakyReLU: 2-2                    --\n│    └─Linear: 2-3                       8,256\n│    └─LeakyReLU: 2-4                    --\n│    └─Linear: 2-5                       2,080\n│    └─ReLU: 2-6                         --\n│    └─Linear: 2-7                       330\n│    └─ReLU: 2-8                         --\n│    └─Linear: 2-9                       22\n=================================================================\nTotal params: 11,712\nTrainable params: 11,712\nNon-trainable params: 0\n================================================================="}, "metadata": {}}], "execution_count": 69}, {"cell_type": "code", "source": "model = nn.DataParallel(model)\nmodel.to(device)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:31.321512Z", "iopub.execute_input": "2025-07-31T09:58:31.322001Z", "iopub.status.idle": "2025-07-31T09:58:31.328307Z", "shell.execute_reply.started": "2025-07-31T09:58:31.321982Z", "shell.execute_reply": "2025-07-31T09:58:31.327857Z"}}, "outputs": [{"execution_count": 70, "output_type": "execute_result", "data": {"text/plain": "DataParallel(\n  (module): Model(\n    (network): Sequential(\n      (0): Linear(in_features=7, out_features=128, bias=True)\n      (1): LeakyReLU(negative_slope=0.01)\n      (2): Linear(in_features=128, out_features=64, bias=True)\n      (3): LeakyReLU(negative_slope=0.01)\n      (4): Linear(in_features=64, out_features=32, bias=True)\n      (5): ReLU()\n      (6): Linear(in_features=32, out_features=10, bias=True)\n      (7): ReLU()\n      (8): Linear(in_features=10, out_features=2, bias=True)\n    )\n  )\n)"}, "metadata": {}}], "execution_count": 70}, {"cell_type": "code", "source": "for epoch in range(vars.EPOCHS):\n    total_epoch_loss = 0\n    for batch_features, batch_labels in dl_train:\n        batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)\n        output = model(batch_features)\n        loss = criterion(output, batch_labels)\n        optimizer.zero_grad()\n        loss.backward()\n        optimizer.step()\n        total_epoch_loss = total_epoch_loss + loss.item()\n    avg_loss = total_epoch_loss/len(dl_train)\n    if (epoch+1)%10 == 0:\n        print(f'Epoch: {epoch + 1} , Loss: {avg_loss}')", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:58:31.328984Z", "iopub.execute_input": "2025-07-31T09:58:31.329181Z", "iopub.status.idle": "2025-07-31T09:59:36.731140Z", "shell.execute_reply.started": "2025-07-31T09:58:31.329165Z", "shell.execute_reply": "2025-07-31T09:59:36.730336Z"}}, "outputs": [{"name": "stdout", "text": "Epoch: 10 , Loss: 0.11791771050508416\nEpoch: 20 , Loss: 0.11764475771593391\nEpoch: 30 , Loss: 0.11877263540726736\nEpoch: 40 , Loss: 0.11828713551368736\nEpoch: 50 , Loss: 0.11879353402951441\nEpoch: 60 , Loss: 0.11928612407331073\nEpoch: 70 , Loss: 0.11999507796942094\nEpoch: 80 , Loss: 0.11965074468780001\nEpoch: 90 , Loss: 0.12014765781136828\nEpoch: 100 , Loss: 0.11992431807955471\nEpoch: 110 , Loss: 0.12013299667507137\nEpoch: 120 , Loss: 0.12018601348572368\nEpoch: 130 , Loss: 0.12025230716264576\nEpoch: 140 , Loss: 0.12051553368021589\nEpoch: 150 , Loss: 0.12066188321337787\nEpoch: 160 , Loss: 0.12095631918775926\nEpoch: 170 , Loss: 0.12065245974979816\nEpoch: 180 , Loss: 0.12095983939911795\nEpoch: 190 , Loss: 0.12171209335258794\nEpoch: 200 , Loss: 0.12090497024767442\nEpoch: 210 , Loss: 0.1211895390046299\nEpoch: 220 , Loss: 0.1214664140118098\nEpoch: 230 , Loss: 0.12161150021017145\nEpoch: 240 , Loss: 0.12201034315272209\nEpoch: 250 , Loss: 0.12152810319574602\n", "output_type": "stream"}], "execution_count": 71}, {"cell_type": "code", "source": "model.eval()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.731928Z", "iopub.execute_input": "2025-07-31T09:59:36.732345Z", "iopub.status.idle": "2025-07-31T09:59:36.737207Z", "shell.execute_reply.started": "2025-07-31T09:59:36.732321Z", "shell.execute_reply": "2025-07-31T09:59:36.736709Z"}}, "outputs": [{"execution_count": 72, "output_type": "execute_result", "data": {"text/plain": "DataParallel(\n  (module): Model(\n    (network): Sequential(\n      (0): Linear(in_features=7, out_features=128, bias=True)\n      (1): LeakyReLU(negative_slope=0.01)\n      (2): Linear(in_features=128, out_features=64, bias=True)\n      (3): LeakyReLU(negative_slope=0.01)\n      (4): Linear(in_features=64, out_features=32, bias=True)\n      (5): ReLU()\n      (6): Linear(in_features=32, out_features=10, bias=True)\n      (7): ReLU()\n      (8): Linear(in_features=10, out_features=2, bias=True)\n    )\n  )\n)"}, "metadata": {}}], "execution_count": 72}, {"cell_type": "code", "source": "outputs = model(torch.from_numpy(X.to_numpy().astype(np.float32)))\n_, predicted = torch.max(outputs, 1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.737945Z", "iopub.execute_input": "2025-07-31T09:59:36.738422Z", "iopub.status.idle": "2025-07-31T09:59:36.782964Z", "shell.execute_reply.started": "2025-07-31T09:59:36.738403Z", "shell.execute_reply": "2025-07-31T09:59:36.782138Z"}}, "outputs": [], "execution_count": 73}, {"cell_type": "code", "source": "off_pred_probs['ANN'] = predicted.cpu().numpy()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.783807Z", "iopub.execute_input": "2025-07-31T09:59:36.784019Z", "iopub.status.idle": "2025-07-31T09:59:36.789141Z", "shell.execute_reply.started": "2025-07-31T09:59:36.784001Z", "shell.execute_reply": "2025-07-31T09:59:36.788411Z"}}, "outputs": [], "execution_count": 74}, {"cell_type": "code", "source": "outputs = model(torch.from_numpy(imp_test.to_numpy().astype(np.float32)))\n_, predicted = torch.max(outputs, 1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.790030Z", "iopub.execute_input": "2025-07-31T09:59:36.790586Z", "iopub.status.idle": "2025-07-31T09:59:36.807531Z", "shell.execute_reply.started": "2025-07-31T09:59:36.790560Z", "shell.execute_reply": "2025-07-31T09:59:36.806924Z"}}, "outputs": [], "execution_count": 75}, {"cell_type": "code", "source": "test_pred_probs['ANN'] = predicted.cpu().numpy()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.808246Z", "iopub.execute_input": "2025-07-31T09:59:36.808465Z", "iopub.status.idle": "2025-07-31T09:59:36.822551Z", "shell.execute_reply.started": "2025-07-31T09:59:36.808449Z", "shell.execute_reply": "2025-07-31T09:59:36.821999Z"}}, "outputs": [], "execution_count": 76}, {"cell_type": "code", "source": "total = 0\ncorrect = 0\n\nwith torch.no_grad():\n    for batch_features, batch_labels in dl_test:\n        batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)\n        outputs = model(batch_features)\n        _, predicted = torch.max(outputs, 1)\n        total = total + batch_labels.shape[0]\n        correct = correct + (predicted == batch_labels).sum().item()\nprint(correct/total)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.823246Z", "iopub.execute_input": "2025-07-31T09:59:36.823455Z", "iopub.status.idle": "2025-07-31T09:59:36.927812Z", "shell.execute_reply.started": "2025-07-31T09:59:36.823430Z", "shell.execute_reply": "2025-07-31T09:59:36.927007Z"}}, "outputs": [{"name": "stdout", "text": "0.9669617793133233\n", "output_type": "stream"}], "execution_count": 77}, {"cell_type": "code", "source": "# evaluation code\ntotal = 0\ncorrect = 0\n\nwith torch.no_grad():\n    for batch_features, batch_labels in dl_train:\n        batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)\n        outputs = model(batch_features)\n        _, predicted = torch.max(outputs, 1)\n        total = total + batch_labels.shape[0]\n        correct = correct + (predicted == batch_labels).sum().item()\nprint(correct/total)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:36.928517Z", "iopub.execute_input": "2025-07-31T09:59:36.928754Z", "iopub.status.idle": "2025-07-31T09:59:37.083431Z", "shell.execute_reply.started": "2025-07-31T09:59:36.928736Z", "shell.execute_reply": "2025-07-31T09:59:37.082686Z"}}, "outputs": [{"name": "stdout", "text": "0.970632692722954\n", "output_type": "stream"}], "execution_count": 78}, {"cell_type": "code", "source": "# Save model's state_dict\n# torch.save(model.state_dict(), 'model_weights.pth')", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.084151Z", "iopub.execute_input": "2025-07-31T09:59:37.084390Z", "iopub.status.idle": "2025-07-31T09:59:37.087659Z", "shell.execute_reply.started": "2025-07-31T09:59:37.084362Z", "shell.execute_reply": "2025-07-31T09:59:37.087069Z"}}, "outputs": [], "execution_count": 79}, {"cell_type": "code", "source": "preds = torch.max(model(test_imp.to(device)), 1).indices\npred_vals = preds.cpu().numpy()\nsam_ann = sam_sub.copy()\nsam_ann['Personality'] = pred_vals\nsam_ann['Personality'] = sam_ann['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_ann.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.088474Z", "iopub.execute_input": "2025-07-31T09:59:37.088888Z", "iopub.status.idle": "2025-07-31T09:59:37.111854Z", "shell.execute_reply.started": "2025-07-31T09:59:37.088867Z", "shell.execute_reply": "2025-07-31T09:59:37.111103Z"}}, "outputs": [{"execution_count": 80, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 80}, {"cell_type": "code", "source": "# sam_ann.to_csv(\"submission.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.112511Z", "iopub.execute_input": "2025-07-31T09:59:37.112706Z", "iopub.status.idle": "2025-07-31T09:59:37.116063Z", "shell.execute_reply.started": "2025-07-31T09:59:37.112691Z", "shell.execute_reply": "2025-07-31T09:59:37.115401Z"}}, "outputs": [], "execution_count": 81}, {"cell_type": "markdown", "source": "<h2><center>Ensemble Model</center></h2>", "metadata": {}}, {"cell_type": "code", "source": "lr_X = off_pred_probs.drop('Index', axis = 1)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.117004Z", "iopub.execute_input": "2025-07-31T09:59:37.117240Z", "iopub.status.idle": "2025-07-31T09:59:37.131120Z", "shell.execute_reply.started": "2025-07-31T09:59:37.117218Z", "shell.execute_reply": "2025-07-31T09:59:37.130521Z"}}, "outputs": [], "execution_count": 82}, {"cell_type": "code", "source": "lr = LogisticRegression()\nlr.fit(lr_X, y)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.131609Z", "iopub.execute_input": "2025-07-31T09:59:37.131826Z", "iopub.status.idle": "2025-07-31T09:59:37.453627Z", "shell.execute_reply.started": "2025-07-31T09:59:37.131810Z", "shell.execute_reply": "2025-07-31T09:59:37.451422Z"}}, "outputs": [{"execution_count": 83, "output_type": "execute_result", "data": {"text/plain": "LogisticRegression()", "text/html": "<style>#sk-container-id-5 {color: black;background-color: white;}#sk-container-id-5 pre{padding: 0;}#sk-container-id-5 div.sk-toggleable {background-color: white;}#sk-container-id-5 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-5 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-5 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-5 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-5 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-5 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-5 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-5 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-5 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-5 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-5 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-5 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-5 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-5 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-5 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-5 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-5 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-5 div.sk-item {position: relative;z-index: 1;}#sk-container-id-5 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-5 div.sk-item::before, #sk-container-id-5 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-5 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-5 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-5 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-5 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-5 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-5 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-5 div.sk-label-container {text-align: center;}#sk-container-id-5 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-5 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-5\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-5\" type=\"checkbox\" checked><label for=\"sk-estimator-id-5\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LogisticRegression</label><div class=\"sk-toggleable__content\"><pre>LogisticRegression()</pre></div></div></div></div></div>"}, "metadata": {}}], "execution_count": 83}, {"cell_type": "code", "source": "off_pred_probs.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.454567Z", "iopub.execute_input": "2025-07-31T09:59:37.454807Z", "iopub.status.idle": "2025-07-31T09:59:37.469948Z", "shell.execute_reply.started": "2025-07-31T09:59:37.454789Z", "shell.execute_reply": "2025-07-31T09:59:37.469334Z"}}, "outputs": [{"execution_count": 84, "output_type": "execute_result", "data": {"text/plain": "   Index   LR  KNN       XGB  LGBM_GBDT  LGBM_GOSS  ANN\n0      1  0.0  0.0  0.037092        0.0        0.0    0\n1      2  0.0  0.0  0.008792        0.0        0.0    0\n2      3  1.0  1.0  0.965504        1.0        1.0    1\n3      4  0.0  0.0  0.006667        0.0        0.0    0\n4      5  0.0  0.0  0.011577        0.0        0.0    0", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Index</th>\n      <th>LR</th>\n      <th>KNN</th>\n      <th>XGB</th>\n      <th>LGBM_GBDT</th>\n      <th>LGBM_GOSS</th>\n      <th>ANN</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.037092</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.008792</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>1.0</td>\n      <td>1.0</td>\n      <td>0.965504</td>\n      <td>1.0</td>\n      <td>1.0</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.006667</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.011577</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 84}, {"cell_type": "code", "source": "test_pred_probs.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.472092Z", "iopub.execute_input": "2025-07-31T09:59:37.472314Z", "iopub.status.idle": "2025-07-31T09:59:37.487216Z", "shell.execute_reply.started": "2025-07-31T09:59:37.472294Z", "shell.execute_reply": "2025-07-31T09:59:37.486543Z"}}, "outputs": [{"execution_count": 85, "output_type": "execute_result", "data": {"text/plain": "   Index   LR  KNN       XGB  LGBM_GBDT  LGBM_GOSS  ANN\n0      1  0.0  0.0  0.001461        0.0        0.0    0\n1      2  1.0  1.0  0.982932        1.0        1.0    1\n2      3  0.0  0.0  0.073560        0.0        0.0    0\n3      4  0.0  0.0 -0.003722        0.0        0.0    0\n4      5  1.0  1.0  0.959560        1.0        1.0    1", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>Index</th>\n      <th>LR</th>\n      <th>KNN</th>\n      <th>XGB</th>\n      <th>LGBM_GBDT</th>\n      <th>LGBM_GOSS</th>\n      <th>ANN</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>1</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.001461</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>2</td>\n      <td>1.0</td>\n      <td>1.0</td>\n      <td>0.982932</td>\n      <td>1.0</td>\n      <td>1.0</td>\n      <td>1</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>3</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0.073560</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>4</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>-0.003722</td>\n      <td>0.0</td>\n      <td>0.0</td>\n      <td>0</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>5</td>\n      <td>1.0</td>\n      <td>1.0</td>\n      <td>0.959560</td>\n      <td>1.0</td>\n      <td>1.0</td>\n      <td>1</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 85}, {"cell_type": "code", "source": "pred = lr.predict(test_pred_probs.drop('Index', axis = 1))\nsam_ens = sam_sub.copy()\nsam_ens['Personality'] = pred_vals\nsam_ens['Personality'] = sam_ens['Personality'].map({0: 'Extrovert', 1 : 'Introvert'})\nsam_ens.head()", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.489363Z", "iopub.execute_input": "2025-07-31T09:59:37.489614Z", "iopub.status.idle": "2025-07-31T09:59:37.507861Z", "shell.execute_reply.started": "2025-07-31T09:59:37.489570Z", "shell.execute_reply": "2025-07-31T09:59:37.505483Z"}}, "outputs": [{"execution_count": 86, "output_type": "execute_result", "data": {"text/plain": "      id Personality\n0  18524   Extrovert\n1  18525   Introvert\n2  18526   Extrovert\n3  18527   Extrovert\n4  18528   Introvert", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>id</th>\n      <th>Personality</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>18524</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>18525</td>\n      <td>Introvert</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>18526</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>18527</td>\n      <td>Extrovert</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>18528</td>\n      <td>Introvert</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "metadata": {}}], "execution_count": 86}, {"cell_type": "code", "source": "sam_ens.to_csv(\"Ensemble_sub.csv\", index = False)", "metadata": {"trusted": true, "execution": {"iopub.status.busy": "2025-07-31T09:59:37.508423Z", "iopub.execute_input": "2025-07-31T09:59:37.509367Z", "iopub.status.idle": "2025-07-31T09:59:37.528862Z", "shell.execute_reply.started": "2025-07-31T09:59:37.509345Z", "shell.execute_reply": "2025-07-31T09:59:37.528257Z"}}, "outputs": [], "execution_count": 87}, {"cell_type": "markdown", "source": "<h2><center>Conclusions</center></h2>\n\nTBF", "metadata": {}}]}